using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IRepository<TeamMember> _teamMemberRepository;
    private readonly IRepository<Testimonial> _testimonialRepository;
    private readonly IRepository<SiteSetting> _siteSettingRepository;
    private readonly IRepository<ContactForm> _contactFormRepository;
    private readonly ILegalPageRepository _legalPageRepository;
    private readonly IAboutPageRepository _aboutPageRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public HomeController(
        ILogger<HomeController> logger,
        IRepository<Service> serviceRepository,
        IRepository<Project> projectRepository,
        IRepository<Technology> technologyRepository,
        IRepository<TeamMember> teamMemberRepository,
        IRepository<Testimonial> testimonialRepository,
        IRepository<SiteSetting> siteSettingRepository,
        IRepository<ContactForm> contactFormRepository,
        ILegalPageRepository legalPageRepository,
        IAboutPageRepository aboutPageRepository,
        IHeroSectionRepository heroSectionRepository)
    {
        _logger = logger;
        _serviceRepository = serviceRepository;
        _projectRepository = projectRepository;
        _technologyRepository = technologyRepository;
        _teamMemberRepository = teamMemberRepository;
        _testimonialRepository = testimonialRepository;
        _siteSettingRepository = siteSettingRepository;
        _contactFormRepository = contactFormRepository;
        _legalPageRepository = legalPageRepository;
        _aboutPageRepository = aboutPageRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index()
    {
        var heroSection = await _heroSectionRepository.GetActiveWithSlidesAsync();
        var services = await _serviceRepository.ListAsync(s => s.IsActive);
        var featuredProjects = await _projectRepository.ListAsync(p => p.IsFeatured);
        var technologies = await _technologyRepository.ListAsync(t => !t.IsDeleted);
        var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);

        ViewBag.HeroSection = heroSection;
        ViewBag.Services = services.AsEnumerable().OrderBy(s => s.DisplayOrder).ToList();
        ViewBag.FeaturedProjects = featuredProjects.AsEnumerable().OrderBy(p => p.DisplayOrder).Take(6).ToList();
        ViewBag.Technologies = technologies.AsEnumerable().OrderBy(t => t.DisplayOrder).Take(8).ToList();
        ViewBag.Testimonials = testimonials.AsEnumerable().OrderBy(t => t.DisplayOrder).Take(3).ToList();

        return View();
    }

    public async Task<IActionResult> About()
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("About");
        var aboutPage = await _aboutPageRepository.GetActiveWithSectionsAsync();
        var teamMembers = await _teamMemberRepository.ListAsync(t => t.IsActive);

        ViewBag.HeroSection = heroSection;
        ViewBag.TeamMembers = teamMembers.OrderBy(t => t.DisplayOrder).ToList();

        if (aboutPage == null)
        {
            // Return default view if no dynamic content exists
            return View();
        }
        return View(aboutPage);
    }

    public async Task<IActionResult> Contact()
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Contact");
        ViewBag.HeroSection = heroSection;

        await LoadContactViewData();
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Contact(ContactForm contactForm)
    {
        try
        {
            Console.WriteLine($"Contact form submitted: Name={contactForm.Name}, Email={contactForm.Email}, Subject={contactForm.Subject}");

            if (ModelState.IsValid)
            {
                // Set timestamps and default values
                contactForm.CreatedAt = DateTime.UtcNow;
                contactForm.UpdatedAt = DateTime.UtcNow;
                contactForm.IsRead = false;
                contactForm.Status = "New";

                Console.WriteLine($"Contact form details before saving: Name={contactForm.Name}, Email={contactForm.Email}, Subject={contactForm.Subject}, Message length={contactForm.Message.Length}");

                var result = await _contactFormRepository.AddAsync(contactForm);
                Console.WriteLine($"Contact form saved successfully with ID: {result.Id}");

                TempData["SuccessMessage"] = "Your message has been sent successfully. We'll get back to you soon!";
                return RedirectToAction(nameof(Contact));
            }
            else
            {
                Console.WriteLine("Contact form ModelState is invalid:");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"Validation error: {error.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in Contact method: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            TempData["ErrorMessage"] = "There was an error sending your message. Please try again.";
        }

        // If validation fails, reload the contact settings and hero section for the view
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Contact");
        ViewBag.HeroSection = heroSection;
        await LoadContactViewData();
        return View(contactForm);
    }

    public async Task<IActionResult> Privacy()
    {
        var privacyPage = await _legalPageRepository.GetBySlugWithSectionsAsync("privacy");
        if (privacyPage == null)
        {
            // Return default view if no dynamic content exists
            return View();
        }
        return View(privacyPage);
    }

    public async Task<IActionResult> Terms()
    {
        var termsPage = await _legalPageRepository.GetBySlugWithSectionsAsync("terms");
        if (termsPage == null)
        {
            // Return default view if no dynamic content exists
            return View();
        }
        return View(termsPage);
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }

    private async Task LoadContactViewData()
    {
        // Get contact-related site settings
        var contactSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Contact" && s.IsPublic);
        var socialSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Social" && s.IsPublic);
        var integrationSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Integration" && s.IsPublic);

        // Pass all contact settings to the view
        ViewBag.ContactSettings = contactSettings.Where(s => !string.IsNullOrEmpty(s.Value)).ToList();

        // Keep individual settings for backward compatibility and specific use cases
        ViewBag.CompanyAddress = contactSettings.FirstOrDefault(s => s.Key == "CompanyAddress")?.Value;
        ViewBag.CompanyPhone = contactSettings.FirstOrDefault(s => s.Key == "CompanyPhone")?.Value;
        ViewBag.CompanyEmail = contactSettings.FirstOrDefault(s => s.Key == "CompanyEmail")?.Value;
        ViewBag.WorkingHours = contactSettings.FirstOrDefault(s => s.Key == "WorkingHours")?.Value;

        // Social media links
        ViewBag.FacebookUrl = socialSettings.FirstOrDefault(s => s.Key == "FacebookUrl")?.Value ?? "#";
        ViewBag.TwitterUrl = socialSettings.FirstOrDefault(s => s.Key == "TwitterUrl")?.Value ?? "#";
        ViewBag.LinkedInUrl = socialSettings.FirstOrDefault(s => s.Key == "LinkedInUrl")?.Value ?? "#";
        ViewBag.InstagramUrl = socialSettings.FirstOrDefault(s => s.Key == "InstagramUrl")?.Value ?? "#";

        // Google Maps coordinates
        ViewBag.GoogleMapsLatitude = integrationSettings.FirstOrDefault(s => s.Key == "GoogleMapsLatitude")?.Value ?? "37.4224764";
        ViewBag.GoogleMapsLongitude = integrationSettings.FirstOrDefault(s => s.Key == "GoogleMapsLongitude")?.Value ?? "-122.0842499";
    }
}
