using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class CareersController : Controller
{
    private readonly IRepository<JobListing> _jobListingRepository;
    private readonly IRepository<JobApplication> _jobApplicationRepository;
    
    public CareersController(
        IRepository<JobListing> jobListingRepository,
        IRepository<JobApplication> jobApplicationRepository)
    {
        _jobListingRepository = jobListingRepository;
        _jobApplicationRepository = jobApplicationRepository;
    }
    
    public async Task<IActionResult> Index()
    {
        var activeJobs = await _jobListingRepository.ListAsync(j => j.IsActive && (!j.ExpiresAt.HasValue || j.ExpiresAt > DateTime.UtcNow));
        return View(activeJobs);
    }
    
    public async Task<IActionResult> Details(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null || !jobListing.IsActive || (jobListing.ExpiresAt.HasValue && jobListing.ExpiresAt < DateTime.UtcNow))
        {
            return NotFound();
        }
        
        return View(jobListing);
    }
    
    public async Task<IActionResult> Apply(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null || !jobListing.IsActive || (jobListing.ExpiresAt.HasValue && jobListing.ExpiresAt < DateTime.UtcNow))
        {
            return NotFound();
        }
        
        var model = new JobApplicationViewModel
        {
            JobListingId = jobListing.Id,
            JobTitle = jobListing.Title
        };
        
        return View(model);
    }
    
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Apply(JobApplicationViewModel model)
    {
        if (ModelState.IsValid)
        {
            var jobListing = await _jobListingRepository.GetByIdAsync(model.JobListingId);
            if (jobListing == null || !jobListing.IsActive || (jobListing.ExpiresAt.HasValue && jobListing.ExpiresAt < DateTime.UtcNow))
            {
                return NotFound();
            }
            
            // Handle resume upload
            string resumeUrl = "/uploads/resumes/default-resume.pdf"; // Default value
            
            if (model.Resume != null && model.Resume.Length > 0)
            {
                // In a real application, you would save the file to a storage service
                // For this example, we'll just use a placeholder URL
                resumeUrl = $"/uploads/resumes/{Guid.NewGuid()}-{model.Resume.FileName}";
            }
            
            var application = new JobApplication
            {
                JobListingId = model.JobListingId,
                ApplicantName = model.Name,
                ApplicantEmail = model.Email,
                ApplicantPhone = model.Phone,
                ResumeUrl = resumeUrl,
                CoverLetter = model.CoverLetter,
                Status = "Pending",
                Notes = ""
            };
            
            await _jobApplicationRepository.AddAsync(application);
            
            TempData["SuccessMessage"] = "Your application has been submitted successfully. We'll review it and get back to you soon!";
            return RedirectToAction(nameof(ThankYou));
        }
        
        return View(model);
    }
    
    public IActionResult ThankYou()
    {
        return View();
    }
}
