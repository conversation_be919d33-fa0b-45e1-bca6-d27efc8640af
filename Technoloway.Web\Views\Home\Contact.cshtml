@model Technoloway.Core.Entities.ContactForm

@{
    ViewData["Title"] = "Contact Us";
    ViewData["MetaDescription"] = "Get in touch with Technoloway for your software development needs. Contact us for a free consultation.";
    ViewData["MetaKeywords"] = "contact us, software development, consultation, get in touch, software services";

    // Helper function to get icon for contact setting
    string GetContactIcon(string key)
    {
        return key.ToLower() switch
        {
            "companyaddress" => "fas fa-map-marker-alt",
            "companyphone" => "fas fa-phone",
            "companyemail" => "fas fa-envelope",
            "workinghours" => "fas fa-clock",
            "companyfax" => "fas fa-fax",
            "companywebsite" => "fas fa-globe",
            "companymobile" => "fas fa-mobile-alt",
            "companywhatsapp" => "fab fa-whatsapp",
            "companytelegram" => "fab fa-telegram",
            "companyskype" => "fab fa-skype",
            _ => "fas fa-info-circle"
        };
    }

    // Helper function to get display name for contact setting
    string GetContactDisplayName(string key)
    {
        return key.ToLower() switch
        {
            "companyaddress" => "Our Location",
            "companyphone" => "Phone Number",
            "companyemail" => "Email Address",
            "workinghours" => "Working Hours",
            "companyfax" => "Fax Number",
            "companywebsite" => "Website",
            "companymobile" => "Mobile Number",
            "companywhatsapp" => "WhatsApp",
            "companytelegram" => "Telegram",
            "companyskype" => "Skype",
            _ => key.Replace("Company", "").Replace("company", "")
        };
    }
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
@Html.Partial("_HeroSection")

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Contact Us</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Get In</span> Touch
                        </h1>
                        <p class="page-subtitle">
                            Ready to start your next project? Let's discuss how we can help bring your ideas to life.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="page-header-visual">
                        <div class="floating-element contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="floating-element phone-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="floating-element location-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="header-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>
}

<!-- Modern Contact Section -->
<section class="modern-section contact-section">
    <div class="container">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="modern-alert success">
                <div class="alert-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="alert-content">
                    <h4>Success!</h4>
                    <p>@TempData["SuccessMessage"]</p>
                </div>
                <button type="button" class="alert-close" onclick="this.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        }

        <div class="row g-5">
            <div class="col-lg-5">
                <div class="contact-info-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="title-highlight">Contact</span> Information
                        </h2>
                        <p class="section-subtitle">
                            Ready to discuss your project? Get in touch with us through any of the following channels.
                        </p>
                    </div>

                    <div class="contact-info-grid">
                        @{
                            var contactSettings = ViewBag.ContactSettings as List<Technoloway.Core.Entities.SiteSetting>;
                        }

                        @if (contactSettings != null && contactSettings.Any())
                        {
                            @foreach (var setting in contactSettings.OrderBy(s => s.Key))
                            {
                                <div class="contact-info-card">
                                    <div class="contact-icon">
                                        @if (!string.IsNullOrEmpty(setting.Icon))
                                        {
                                            @if (setting.Icon.StartsWith("fa") || setting.Icon.Contains("fa-"))
                                            {
                                                <i class="@setting.Icon"></i>
                                            }
                                            else
                                            {
                                                <img src="@setting.Icon" alt="@setting.Key icon" />
                                            }
                                        }
                                        else
                                        {
                                            <i class="@GetContactIcon(setting.Key)"></i>
                                        }
                                    </div>
                                    <div class="contact-info-content">
                                        <h4 class="contact-info-title">@GetContactDisplayName(setting.Key)</h4>
                                        <div class="contact-info-value">
                                            @if (setting.Key.ToLower().Contains("email"))
                                            {
                                                <a href="mailto:@setting.Value" class="contact-link">@setting.Value</a>
                                            }
                                            else if (setting.Key.ToLower().Contains("phone") || setting.Key.ToLower().Contains("mobile"))
                                            {
                                                <a href="tel:@setting.Value" class="contact-link">@setting.Value</a>
                                            }
                                            else if (setting.Key.ToLower().Contains("website"))
                                            {
                                                <a href="@setting.Value" target="_blank" class="contact-link">@setting.Value</a>
                                            }
                                            else if (setting.Key.ToLower().Contains("whatsapp"))
                                            {
                                                <a href="https://wa.me/@setting.Value.Replace("+", "").Replace(" ", "").Replace("-", "")" target="_blank" class="contact-link">@setting.Value</a>
                                            }
                                            else
                                            {
                                                <span>@setting.Value</span>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="contact-info-placeholder">
                                <div class="placeholder-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <p>Contact information will be displayed here when available.</p>
                            </div>
                        }
                    </div>

                    <div class="social-section">
                        <h4 class="social-title">Follow Us</h4>
                        <div class="social-links">
                            <a href="@ViewBag.FacebookUrl" class="social-link facebook" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                                <span>Facebook</span>
                            </a>
                            <a href="@ViewBag.TwitterUrl" class="social-link twitter" target="_blank">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </a>
                            <a href="@ViewBag.LinkedInUrl" class="social-link linkedin" target="_blank">
                                <i class="fab fa-linkedin-in"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="@ViewBag.InstagramUrl" class="social-link instagram" target="_blank">
                                <i class="fab fa-instagram"></i>
                                <span>Instagram</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-7">
                <div class="contact-form-section">
                    <div class="modern-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <span class="title-highlight">Send Us</span> a Message
                            </h3>
                            <p class="card-subtitle">
                                Fill out the form below and we'll get back to you within 24 hours.
                            </p>
                        </div>

                        <div class="card-body">
                            @if (TempData["ErrorMessage"] != null)
                            {
                                <div class="modern-alert error">
                                    <div class="alert-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="alert-content">
                                        <h4>Error!</h4>
                                        <p>@TempData["ErrorMessage"]</p>
                                    </div>
                                </div>
                            }

                            <form asp-action="Contact" method="post" class="modern-form">
                                <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label asp-for="Name" class="form-label">
                                            <i class="fas fa-user"></i>
                                            <span>Your Name</span>
                                        </label>
                                        <input asp-for="Name" class="form-input" placeholder="Enter your full name" />
                                        <span asp-validation-for="Name" class="form-error"></span>
                                    </div>

                                    <div class="form-group">
                                        <label asp-for="Email" class="form-label">
                                            <i class="fas fa-envelope"></i>
                                            <span>Email Address</span>
                                        </label>
                                        <input asp-for="Email" class="form-input" placeholder="Enter your email address" />
                                        <span asp-validation-for="Email" class="form-error"></span>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label asp-for="Phone" class="form-label">
                                            <i class="fas fa-phone"></i>
                                            <span>Phone Number</span>
                                        </label>
                                        <input asp-for="Phone" class="form-input" placeholder="Enter your phone number" />
                                        <span asp-validation-for="Phone" class="form-error"></span>
                                    </div>

                                    <div class="form-group">
                                        <label asp-for="Subject" class="form-label">
                                            <i class="fas fa-tag"></i>
                                            <span>Subject</span>
                                        </label>
                                        <input asp-for="Subject" class="form-input" placeholder="What's this about?" />
                                        <span asp-validation-for="Subject" class="form-error"></span>
                                    </div>
                                </div>

                                <div class="form-group full-width">
                                    <label asp-for="Message" class="form-label">
                                        <i class="fas fa-comment"></i>
                                        <span>Your Message</span>
                                    </label>
                                    <textarea asp-for="Message" class="form-textarea" rows="6" placeholder="Tell us about your project or inquiry..."></textarea>
                                    <span asp-validation-for="Message" class="form-error"></span>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="modern-btn primary large">
                                        <span class="btn-text">Send Message</span>
                                        <span class="btn-icon">
                                            <i class="fas fa-paper-plane"></i>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Map Section -->
<section class="modern-section map-section">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">
                <span class="title-highlight">Find Us</span> on the Map
            </h2>
            <p class="section-subtitle">
                Visit our office or get directions to our location
            </p>
        </div>

        <div class="map-container">
            <div class="map-wrapper">
                <div id="map" class="modern-map">
                    <!-- Fallback content that will be replaced by JavaScript -->
                    <div class="map-fallback-container">
                        <div class="fallback-content">
                            <div class="fallback-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h4>Technoloway Office</h4>
                            <p>Visit us at our headquarters</p>
                            <div class="fallback-address">
                                <p><i class="fas fa-map-marker-alt me-2"></i>123 Business Street, Tech City, TC 12345</p>
                                <p><i class="fas fa-phone me-2"></i>+****************</p>
                                <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <div class="fallback-actions">
                                <button onclick="openDirections()" class="modern-btn primary small">
                                    <span class="btn-text">Get Directions</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-directions"></i>
                                    </span>
                                </button>
                                <button onclick="openInGoogleMaps()" class="modern-btn secondary small">
                                    <span class="btn-text">View on Google Maps</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-external-link-alt"></i>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script src="~/js/homepage-animations.js" asp-append-version="true"></script>
    <script>
        // Global variables
        let mapInitialized = false;
        const defaultLocation = { lat: 40.7128, lng: -74.0060 }; // New York coordinates

        // Show map fallback immediately
        function showMapFallback() {
            const mapElement = document.getElementById("map");
            if (mapElement) {
                mapElement.innerHTML = `
                    <div class="map-fallback-container">
                        <div class="fallback-content">
                            <div class="fallback-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h4>Technoloway Office</h4>
                            <p>Visit us at our headquarters</p>
                            <div class="fallback-address">
                                <p><i class="fas fa-map-marker-alt me-2"></i>123 Business Street, Tech City, TC 12345</p>
                                <p><i class="fas fa-phone me-2"></i>+****************</p>
                                <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <div class="fallback-actions">
                                <button onclick="openDirections()" class="modern-btn primary small">
                                    <span class="btn-text">Get Directions</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-directions"></i>
                                    </span>
                                </button>
                                <button onclick="openInGoogleMaps()" class="modern-btn secondary small">
                                    <span class="btn-text">View on Google Maps</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-external-link-alt"></i>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Get directions function
        function openDirections() {
            const lat = @(ViewBag.GoogleMapsLatitude ?? 40.7128);
            const lng = @(ViewBag.GoogleMapsLongitude ?? -74.0060);
            const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
            window.open(url, '_blank');
        }

        // Open in Google Maps
        function openInGoogleMaps() {
            const lat = @(ViewBag.GoogleMapsLatitude ?? 40.7128);
            const lng = @(ViewBag.GoogleMapsLongitude ?? -74.0060);
            const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
            window.open(url, '_blank');
        }

        // Initialize with fallback immediately
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing map fallback...');

            // Show fallback immediately for better UX
            setTimeout(() => {
                console.log('Calling showMapFallback...');
                showMapFallback();
            }, 100);

            // Try to load Google Maps as enhancement
            setTimeout(tryLoadGoogleMaps, 1000);
        });

        // Try to load Google Maps (optional enhancement)
        function tryLoadGoogleMaps() {
            // Skip Google Maps loading for now to avoid API issues
            // This can be enabled later when you have a valid API key
            console.log('Google Maps loading skipped - using fallback display');
            return;

            // Uncomment below when you have a valid Google Maps API key
            /*
            if (mapInitialized) return;

            try {
                const script = document.createElement('script');
                script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_VALID_API_KEY&callback=initGoogleMap';
                script.async = true;
                script.defer = true;
                script.onerror = function() {
                    console.log('Google Maps failed to load, keeping fallback');
                };
                document.head.appendChild(script);
            } catch (error) {
                console.log('Error loading Google Maps:', error);
            }
            */
        }

        // Google Maps initialization (when API is available)
        function initGoogleMap() {
            if (mapInitialized) return;

            try {
                const location = {
                    lat: @(ViewBag.GoogleMapsLatitude ?? 40.7128),
                    lng: @(ViewBag.GoogleMapsLongitude ?? -74.0060)
                };

                const mapOptions = {
                    zoom: 15,
                    center: location,
                    styles: [
                        {
                            "featureType": "water",
                            "elementType": "all",
                            "stylers": [{"color": "#667eea"}]
                        },
                        {
                            "featureType": "landscape",
                            "elementType": "all",
                            "stylers": [{"color": "#f8fafc"}]
                        }
                    ],
                    disableDefaultUI: false,
                    zoomControl: true,
                    mapTypeControl: false,
                    scaleControl: true,
                    streetViewControl: true,
                    rotateControl: false,
                    fullscreenControl: true
                };

                const map = new google.maps.Map(document.getElementById("map"), mapOptions);

                const marker = new google.maps.Marker({
                    position: location,
                    map: map,
                    title: "Technoloway",
                    animation: google.maps.Animation.DROP
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 15px; font-family: 'Inter', sans-serif; max-width: 250px;">
                            <h4 style="margin: 0 0 8px 0; color: #1f2937; font-size: 16px;">Technoloway</h4>
                            <p style="margin: 0 0 10px 0; color: #6b7280; font-size: 14px;">Software Development Company</p>
                            <button onclick="openDirections()" style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 12px; cursor: pointer;">
                                Get Directions
                            </button>
                        </div>
                    `
                });

                marker.addListener('click', function() {
                    infoWindow.open(map, marker);
                });

                setTimeout(() => {
                    infoWindow.open(map, marker);
                }, 1000);

                mapInitialized = true;

            } catch (error) {
                console.error('Error initializing Google Map:', error);
            }
        }

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Form input animations
            const formInputs = document.querySelectorAll('.form-input, .form-textarea');

            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // Check if input has value on load
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });

            // Contact info card animations
            const contactCards = document.querySelectorAll('.contact-info-card');

            contactCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-fade-in-up');
            });

            // Social link hover effects
            const socialLinks = document.querySelectorAll('.social-link');

            socialLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Form submission enhancement
            const contactForm = document.querySelector('.modern-form');
            const submitBtn = document.querySelector('.modern-btn.primary');

            if (contactForm && submitBtn) {
                contactForm.addEventListener('submit', function(e) {
                    submitBtn.classList.add('loading');
                    submitBtn.innerHTML = `
                        <span class="btn-text">Sending...</span>
                        <span class="btn-icon">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    `;
                });
            }

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
                element.classList.add('floating-animation');
            });

            // Contact link interactions
            const contactLinks = document.querySelectorAll('.contact-link');

            contactLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Add ripple effect
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple-effect');
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.contact-info-section, .contact-form-section, .map-container');
            animateElements.forEach(el => observer.observe(el));
        });
    </script>

    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
