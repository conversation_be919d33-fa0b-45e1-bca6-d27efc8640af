@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Create Hero Section";
    ViewData["PageTitle"] = "Create Hero Section";
    ViewData["PageDescription"] = "Create a new hero section for your website pages";
}

<style>
    .form-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 1.5rem;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .form-body {
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 1px solid #e1e5e9;
        border-radius: 0.375rem;
        padding: 0.75rem;
        transition: all 0.15s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.35);
    }

    .required {
        color: #dc3545;
    }

    .nav-tabs .nav-link {
        border: none;
        border-bottom: 2px solid transparent;
        color: #6c757d;
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        border-bottom-color: #667eea;
        color: #667eea;
        background: none;
        position: relative;
    }

    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px 2px 0 0;
    }

    .tab-content {
        padding-top: 1.5rem;
        min-height: 400px;
    }

    .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }

    @@keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .slide-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .slide-item h6 {
        color: #495057;
        font-weight: 600;
    }

    .media-fields {
        background: white;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>



<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-plus me-2 text-primary"></i>Create Hero Section
            </h1>
            <p class="text-muted mb-0">Create a new hero section for your website pages</p>
        </div>
        <div class="btn-group">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Validation Summary -->
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <div asp-validation-summary="All" class="mb-0"></div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <form asp-action="Create" method="post" id="heroSectionForm" class="needs-validation" novalidate>
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="heroSectionTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">
                    <i class="fas fa-info-circle me-1"></i>Basic Info
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                    <i class="fas fa-edit me-1"></i>Content
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="buttons-tab" data-bs-toggle="tab" data-bs-target="#buttons" type="button" role="tab" aria-controls="buttons" aria-selected="false">
                    <i class="fas fa-mouse-pointer me-1"></i>CTA Buttons
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="slides-tab" data-bs-toggle="tab" data-bs-target="#slides" type="button" role="tab" aria-controls="slides" aria-selected="false">
                    <i class="fas fa-images me-1"></i>Slides
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">
                    <i class="fas fa-cogs me-1"></i>Settings
                </button>
            </li>
        </ul>

        <div class="tab-content" id="heroSectionTabContent">
            <!-- Basic Information Tab -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Title" class="form-label">
                                        Title <span class="required">*</span>
                                    </label>
                                    <input asp-for="Title" class="form-control" required />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PageName" class="form-label">
                                        Page <span class="required">*</span>
                                    </label>
                                    <select asp-for="PageName" class="form-select" required>
                                        <option value="">Select Page</option>
                                        <option value="Home">Home</option>
                                        <option value="About">About</option>
                                        <option value="Services">Services</option>
                                        <option value="Projects">Projects</option>
                                        <option value="Contact">Contact</option>
                                        <option value="Blog">Blog</option>
                                        <option value="Careers">Careers</option>
                                    </select>
                                    <span asp-validation-for="PageName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaDescription" class="form-label">Meta Description</label>
                            <textarea asp-for="MetaDescription" class="form-control" rows="3"
                                      placeholder="Brief description for search engines"></textarea>
                            <span asp-validation-for="MetaDescription" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaKeywords" class="form-label">Meta Keywords</label>
                            <input asp-for="MetaKeywords" class="form-control"
                                   placeholder="Comma-separated keywords for SEO" />
                            <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Tab -->
            <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>Hero Content
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="MainTitle" class="form-label">
                                Main Title <span class="required">*</span>
                            </label>
                            <textarea asp-for="MainTitle" class="form-control ckeditor" rows="3" required></textarea>
                            <span asp-validation-for="MainTitle" class="text-danger"></span>
                            <small class="form-text text-muted">HTML tags allowed for styling</small>
                        </div>

                        <div class="form-group">
                            <label asp-for="MainSubtitle" class="form-label">Main Subtitle</label>
                            <textarea asp-for="MainSubtitle" class="form-control ckeditor" rows="2"></textarea>
                            <span asp-validation-for="MainSubtitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="MainDescription" class="form-label">Main Description</label>
                            <textarea asp-for="MainDescription" class="form-control ckeditor" rows="4"></textarea>
                            <span asp-validation-for="MainDescription" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CTA Buttons Tab -->
            <div class="tab-pane fade" id="buttons" role="tabpanel" aria-labelledby="buttons-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-mouse-pointer me-2"></i>Call-to-Action Buttons
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Primary Button</h6>
                                <div class="form-group">
                                    <label asp-for="PrimaryButtonText" class="form-label">Button Text</label>
                                    <input asp-for="PrimaryButtonText" class="form-control" />
                                    <span asp-validation-for="PrimaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="PrimaryButtonUrl" class="form-label">Button URL</label>
                                    <input asp-for="PrimaryButtonUrl" class="form-control" />
                                    <span asp-validation-for="PrimaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-secondary mb-3">Secondary Button</h6>
                                <div class="form-group">
                                    <label asp-for="SecondaryButtonText" class="form-label">Button Text</label>
                                    <input asp-for="SecondaryButtonText" class="form-control" />
                                    <span asp-validation-for="SecondaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="SecondaryButtonUrl" class="form-label">Button URL</label>
                                    <input asp-for="SecondaryButtonUrl" class="form-control" />
                                    <span asp-validation-for="SecondaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slides Tab -->
            <div class="tab-pane fade" id="slides" role="tabpanel" aria-labelledby="slides-tab">
                <div class="form-card">
                    <div class="form-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>Hero Slides
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addSlide()">
                            <i class="fas fa-plus me-1"></i>Add Slide
                        </button>
                    </div>
                    <div class="form-body">
                        <div id="slides-container">
                            <!-- Slides will be added here dynamically -->
                        </div>
                        <div class="text-muted text-center py-4" id="no-slides-message">
                            <i class="fas fa-images fa-3x mb-3 opacity-50"></i>
                            <p>No slides added yet. Click "Add Slide" to create your first slide.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>Hero Settings
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input asp-for="EnableSlideshow" class="form-check-input" />
                                    <label asp-for="EnableSlideshow" class="form-check-label">Enable Slideshow</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="AutoPlay" class="form-check-input" />
                                    <label asp-for="AutoPlay" class="form-check-label">Auto Play</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="ShowDots" class="form-check-input" />
                                    <label asp-for="ShowDots" class="form-check-label">Show Navigation Dots</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="ShowArrows" class="form-check-input" />
                                    <label asp-for="ShowArrows" class="form-check-label">Show Navigation Arrows</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="SlideshowSpeed" class="form-label">Slideshow Speed (ms)</label>
                                    <input asp-for="SlideshowSpeed" class="form-control" type="number" min="1000" max="30000" step="1000" />
                                    <span asp-validation-for="SlideshowSpeed" class="text-danger"></span>
                                </div>
                                <div class="form-check mb-3">
                                    <input asp-for="EnableFloatingElements" class="form-check-input" />
                                    <label asp-for="EnableFloatingElements" class="form-check-label">Enable Floating Elements</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
            </div>
            <div>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Create Hero Section
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        // Initialize Bootstrap tabs - Enhanced debugging version
        function initializeTabs() {
            console.log('🚀 Starting tab initialization...');

            // Add visual indicator
            document.body.style.border = '3px solid red';
            setTimeout(() => {
                document.body.style.border = 'none';
            }, 2000);

            // Check if Bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error('❌ Bootstrap is not loaded!');
                alert('Bootstrap is not loaded!');
                return;
            }
            console.log('✅ Bootstrap is available');

            // Initialize all tab triggers
            const tabTriggerList = document.querySelectorAll('#heroSectionTabs button[data-bs-toggle="tab"]');
            console.log('🔍 Found tab triggers:', tabTriggerList.length);

            // Log each tab trigger
            tabTriggerList.forEach((trigger, index) => {
                console.log(`Tab ${index + 1}:`, {
                    id: trigger.id,
                    target: trigger.getAttribute('data-bs-target'),
                    text: trigger.textContent.trim()
                });
            });

            if (tabTriggerList.length === 0) {
                console.error('❌ No tab triggers found!');
                alert('No tab triggers found!');
                return;
            }

            // Initialize Bootstrap Tab instances
            const tabList = [...tabTriggerList].map(tabTriggerEl => {
                console.log('🔧 Initializing tab:', tabTriggerEl.getAttribute('data-bs-target'));
                return new bootstrap.Tab(tabTriggerEl);
            });

            // Add click event listeners with multiple approaches
            tabTriggerList.forEach(function(tabTrigger, index) {
                console.log(`🔧 Adding click listener to tab ${index + 1}`);

                // Method 1: Direct click event
                tabTrigger.onclick = function(event) {
                    event.preventDefault();
                    console.log(`🖱️ ONCLICK - Create Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    switchTab(this, tabTriggerList);
                    return false;
                };

                // Method 2: addEventListener
                tabTrigger.addEventListener('click', function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.log(`🖱️ ADDEVENTLISTENER - Create Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    switchTab(this, tabTriggerList);
                    return false;
                }, true);

                // Method 3: mousedown event as backup
                tabTrigger.addEventListener('mousedown', function(event) {
                    console.log(`🖱️ MOUSEDOWN - Create Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    setTimeout(() => switchTab(this, tabTriggerList), 10);
                });
            });

            // Function to handle tab switching
            function switchTab(clickedTab, allTabs) {
                console.log('🔄 Switching tab to:', clickedTab.getAttribute('data-bs-target'));

                // Remove active class from all tabs
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                    tab.setAttribute('aria-selected', 'false');
                });

                // Add active class to clicked tab
                clickedTab.classList.add('active');
                clickedTab.setAttribute('aria-selected', 'true');

                // Hide all tab panes
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Show target tab pane
                const targetId = clickedTab.getAttribute('data-bs-target');
                console.log('🎯 Looking for tab pane with ID:', targetId);

                // Try multiple selector approaches
                let targetPane = document.querySelector(targetId);
                console.log('🔍 Method 1 - querySelector result:', targetPane);

                if (!targetPane) {
                    // Try without the # symbol
                    const idWithoutHash = targetId.substring(1);
                    targetPane = document.getElementById(idWithoutHash);
                    console.log('🔍 Method 2 - getElementById result:', targetPane);
                }

                if (!targetPane) {
                    // Try finding within the tab content container
                    const tabContent = document.getElementById('heroSectionTabContent');
                    if (tabContent) {
                        targetPane = tabContent.querySelector(targetId);
                        console.log('🔍 Method 3 - within tab content result:', targetPane);
                    }
                }

                if (!targetPane) {
                    // Try finding by class and id combination
                    targetPane = document.querySelector(`.tab-pane${targetId}`);
                    console.log('🔍 Method 4 - class+id selector result:', targetPane);
                }

                if (targetPane) {
                    console.log('📋 Target pane current classes:', targetPane.className);
                    targetPane.classList.add('show', 'active');
                    console.log('📋 Target pane new classes:', targetPane.className);
                    console.log('✅ Create Tab pane shown:', targetId);

                    // Force a visual check
                    targetPane.style.display = 'block';
                    targetPane.style.opacity = '1';
                    console.log('🎨 Applied inline styles for visibility');
                } else {
                    console.error('❌ Target pane not found:', targetId);
                    console.log('🔍 Available tab panes:');
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        console.log('  - ID:', pane.id, 'Classes:', pane.className);
                    });
                }
            }

            // Handle tab shown event
            tabTriggerList.forEach(function(tabTrigger) {
                tabTrigger.addEventListener('shown.bs.tab', function(event) {
                    console.log('📋 Tab shown event:', event.target.getAttribute('data-bs-target'));
                });
            });

            console.log('✅ Tabs initialized successfully');
            alert('Tabs initialized! Check console for details.');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs after a short delay to ensure DOM is ready
            setTimeout(() => {
                initializeTabs();
            }, 100);

            // Initialize CKEditor for all textareas with ckeditor class
            document.querySelectorAll('.ckeditor').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'bold', 'italic', '|',
                                'link', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('CKEditor initialized for:', textarea.name);
                    })
                    .catch(error => {
                        console.error('CKEditor initialization error:', error);
                    });
            });
        });

        // Slide management
        let slideIndex = 0;

        function addSlide() {
            const container = document.getElementById('slides-container');
            const noSlidesMessage = document.getElementById('no-slides-message');

            const slideHtml = `
                <div class="slide-item border rounded p-3 mb-3" data-slide-index="${slideIndex}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Slide ${slideIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSlide(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <input type="hidden" name="Slides[${slideIndex}].Id" value="0" />
                    <input type="hidden" name="Slides[${slideIndex}].MediaType" value="image" />
                    <input type="hidden" name="Slides[${slideIndex}].VideoAutoPlay" value="true" />
                    <input type="hidden" name="Slides[${slideIndex}].VideoMuted" value="true" />
                    <input type="hidden" name="Slides[${slideIndex}].VideoLoop" value="true" />
                    <input type="hidden" name="Slides[${slideIndex}].VideoControls" value="false" />

                    <div class="mb-3">
                        <label class="form-label required">Slide Content</label>
                        <textarea name="Slides[${slideIndex}].Content" class="form-control ckeditor-full" rows="8" required></textarea>
                        <div class="form-text">Use rich text formatting to create your slide content. You can format text, add colors, change fonts, and more.</div>
                    </div>

                    <!-- Media Type Selection -->
                    <div class="mb-3">
                        <label class="form-label required">Media Type</label>
                        <select name="Slides[${slideIndex}].MediaType" class="form-select media-type-select" data-slide-index="${slideIndex}">
                            <option value="image">Image</option>
                            <option value="video">Video</option>
                        </select>
                    </div>

                    <!-- Image Fields -->
                    <div class="media-fields image-fields" data-media-type="image" data-slide-index="${slideIndex}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Image URL</label>
                                    <input name="Slides[${slideIndex}].ImageUrl" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Media Alt Text</label>
                                    <input name="Slides[${slideIndex}].MediaAlt" class="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Video Fields -->
                    <div class="media-fields video-fields" data-media-type="video" data-slide-index="${slideIndex}" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Video URL</label>
                                    <input name="Slides[${slideIndex}].VideoUrl" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Media Alt Text</label>
                                    <input name="Slides[${slideIndex}].MediaAlt" class="form-control" />
                                </div>
                            </div>
                        </div>

                        <!-- Video Settings -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoAutoPlay" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Video Auto Play</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoMuted" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Video Muted</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoLoop" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Video Loop</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input name="Slides[${slideIndex}].VideoControls" class="form-check-input" type="checkbox" />
                                    <label class="form-check-label">Video Controls</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Button Text</label>
                                <input name="Slides[${slideIndex}].ButtonText" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Button URL</label>
                                <input name="Slides[${slideIndex}].ButtonUrl" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Display Order</label>
                                <input name="Slides[${slideIndex}].DisplayOrder" class="form-control" type="number" min="1" value="${slideIndex + 1}" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Animation Type</label>
                                <select name="Slides[${slideIndex}].AnimationType" class="form-select">
                                    <option value="fade">Fade</option>
                                    <option value="slide">Slide</option>
                                    <option value="zoom">Zoom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Duration (ms)</label>
                                <input name="Slides[${slideIndex}].Duration" class="form-control" type="number" min="1000" max="30000" step="1000" value="5000" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input name="Slides[${slideIndex}].IsActive" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Is Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', slideHtml);
            slideIndex++;
            updateSlideNumbers();
            initializeMediaTypeHandlers();

            // Hide no slides message
            if (noSlidesMessage) {
                noSlidesMessage.style.display = 'none';
            }

            // Initialize CKEditor for newly added textareas
            const newSlide = container.lastElementChild;
            newSlide.querySelectorAll('.ckeditor-full').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                                'bold', 'italic', '|',
                                'alignment', '|',
                                'link', 'insertImage', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ],
                            shouldNotGroupWhenFull: true
                        },
                        fontSize: {
                            options: [
                                9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                            ]
                        },
                        fontFamily: {
                            options: [
                                'default',
                                'Arial, Helvetica, sans-serif',
                                'Courier New, Courier, monospace',
                                'Georgia, serif',
                                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                                'Tahoma, Geneva, sans-serif',
                                'Times New Roman, Times, serif',
                                'Trebuchet MS, Helvetica, sans-serif',
                                'Verdana, Geneva, sans-serif',
                                'Inter, sans-serif',
                                'Poppins, sans-serif',
                                'Lato, sans-serif'
                            ]
                        },
                        fontColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        fontBackgroundColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        image: {
                            toolbar: [
                                'imageTextAlternative',
                                'imageStyle:inline',
                                'imageStyle:block',
                                'imageStyle:side'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('Full CKEditor initialized for new slide:', textarea.name);
                    })
                    .catch(error => {
                        console.error('Full CKEditor initialization error:', error);
                    });
            });
        }

        function removeSlide(button) {
            const slideItem = button.closest('.slide-item');
            const container = document.getElementById('slides-container');
            const noSlidesMessage = document.getElementById('no-slides-message');

            slideItem.remove();
            updateSlideNumbers();

            // Show no slides message if no slides left
            if (container.children.length === 0 && noSlidesMessage) {
                noSlidesMessage.style.display = 'block';
            }
        }

        function updateSlideNumbers() {
            const slides = document.querySelectorAll('.slide-item');
            slides.forEach((slide, index) => {
                const title = slide.querySelector('h6');
                if (title) {
                    title.textContent = `Slide ${index + 1}`;
                }
            });
        }

        function initializeMediaTypeHandlers() {
            document.querySelectorAll('.media-type-select').forEach(function(select) {
                select.addEventListener('change', function() {
                    const slideIndex = this.dataset.slideIndex;
                    const imageFields = document.querySelector(`.image-fields[data-slide-index="${slideIndex}"]`);
                    const videoFields = document.querySelector(`.video-fields[data-slide-index="${slideIndex}"]`);

                    if (this.value === 'image') {
                        imageFields.style.display = 'block';
                        videoFields.style.display = 'none';
                    } else {
                        imageFields.style.display = 'none';
                        videoFields.style.display = 'block';
                    }
                });
            });
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
}
