@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Create Hero Section";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}





<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="display-6 mb-2">
                <i class="fas fa-plus-circle me-3"></i>Create Hero Section
            </h1>
            <p class="lead mb-0 opacity-90">Design and configure a new hero section for your website pages</p>
        </div>
    </div>

    <!-- Validation Summary -->
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <div asp-validation-summary="All" class="mb-0"></div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <form asp-action="Create" method="post" id="heroSectionForm" class="needs-validation" novalidate>
        <div class="row">
            <div class="col-lg-8">
                <!-- Sticky Action Bar -->
                <div class="sticky-actions">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Create Hero Section
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Basic Information Section -->
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                            <span class="badge ms-auto">Required</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Title" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Title<span class="required">*</span>
                                    </label>
                                    <input asp-for="Title" class="form-control" required
                                           placeholder="Enter hero section title" />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PageName" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i>Target Page<span class="required">*</span>
                                    </label>
                                    <select asp-for="PageName" class="form-select" required>
                                        <option value="">Select Page</option>
                                        <option value="Home">Home</option>
                                        <option value="About">About</option>
                                        <option value="Services">Services</option>
                                        <option value="Projects">Projects</option>
                                        <option value="Contact">Contact</option>
                                        <option value="Blog">Blog</option>
                                        <option value="Careers">Careers</option>
                                    </select>
                                    <span asp-validation-for="PageName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="MetaDescription" class="form-label">
                                        <i class="fas fa-search me-1"></i>Meta Description
                                    </label>
                                    <textarea asp-for="MetaDescription" class="form-control" rows="3"
                                              placeholder="Brief description for search engines (150-160 characters)"></textarea>
                                    <span asp-validation-for="MetaDescription" class="text-danger"></span>
                                    <small class="form-text text-muted">Used by search engines in search results</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="MetaKeywords" class="form-label">
                                        <i class="fas fa-tags me-1"></i>Meta Keywords
                                    </label>
                                    <input asp-for="MetaKeywords" class="form-control"
                                           placeholder="keyword1, keyword2, keyword3" />
                                    <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                                    <small class="form-text text-muted">Comma-separated keywords for SEO</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check form-switch">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">
                                    <i class="fas fa-toggle-on me-1"></i>Active Status
                                </label>
                                <small class="form-text text-muted d-block">Enable this hero section for display</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hero Content Section -->
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-edit me-2"></i>Hero Content
                            <span class="badge ms-auto">Primary</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="form-group">
                            <label asp-for="MainTitle" class="form-label">
                                <i class="fas fa-heading me-1"></i>Main Title<span class="required">*</span>
                            </label>
                            <textarea asp-for="MainTitle" class="form-control ckeditor" rows="3" required
                                      placeholder="Enter the main hero title that will grab attention"></textarea>
                            <span asp-validation-for="MainTitle" class="text-danger"></span>
                            <small class="form-text text-muted">This is the primary headline visitors will see. HTML formatting supported.</small>
                        </div>

                        <div class="form-group">
                            <label asp-for="MainSubtitle" class="form-label">
                                <i class="fas fa-text-height me-1"></i>Main Subtitle<span class="required">*</span>
                            </label>
                            <textarea asp-for="MainSubtitle" class="form-control ckeditor" rows="2" required
                                      placeholder="Enter a compelling subtitle that supports the main title"></textarea>
                            <span asp-validation-for="MainSubtitle" class="text-danger"></span>
                            <small class="form-text text-muted">Supporting text that provides more context to your main title.</small>
                        </div>

                        <div class="form-group">
                            <label asp-for="MainDescription" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Main Description
                            </label>
                            <textarea asp-for="MainDescription" class="form-control ckeditor" rows="4"
                                      placeholder="Optional detailed description to provide more information"></textarea>
                            <span asp-validation-for="MainDescription" class="text-danger"></span>
                            <small class="form-text text-muted">Additional descriptive text to explain your offering in more detail.</small>
                        </div>
                    </div>
                </div>

                <!-- Call-to-Action Buttons Section -->
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-mouse-pointer me-2"></i>Call-to-Action Buttons
                            <span class="badge ms-auto">Optional</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-star me-1"></i>Primary Button</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label asp-for="PrimaryButtonText" class="form-label">
                                                <i class="fas fa-font me-1"></i>Button Text
                                            </label>
                                            <input asp-for="PrimaryButtonText" class="form-control"
                                                   placeholder="e.g., Get Started, Learn More" />
                                            <span asp-validation-for="PrimaryButtonText" class="text-danger"></span>
                                        </div>
                                        <div class="form-group">
                                            <label asp-for="PrimaryButtonUrl" class="form-label">
                                                <i class="fas fa-link me-1"></i>Button URL
                                            </label>
                                            <input asp-for="PrimaryButtonUrl" class="form-control"
                                                   placeholder="/contact, https://example.com" />
                                            <span asp-validation-for="PrimaryButtonUrl" class="text-danger"></span>
                                            <small class="form-text text-muted">Use relative URLs (/page) or absolute URLs (https://...)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0"><i class="fas fa-circle me-1"></i>Secondary Button</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label asp-for="SecondaryButtonText" class="form-label">
                                                <i class="fas fa-font me-1"></i>Button Text
                                            </label>
                                            <input asp-for="SecondaryButtonText" class="form-control"
                                                   placeholder="e.g., View Portfolio, Contact Us" />
                                            <span asp-validation-for="SecondaryButtonText" class="text-danger"></span>
                                        </div>
                                        <div class="form-group">
                                            <label asp-for="SecondaryButtonUrl" class="form-label">
                                                <i class="fas fa-link me-1"></i>Button URL
                                            </label>
                                            <input asp-for="SecondaryButtonUrl" class="form-control"
                                                   placeholder="/projects, /about" />
                                            <span asp-validation-for="SecondaryButtonUrl" class="text-danger"></span>
                                            <small class="form-text text-muted">Use relative URLs (/page) or absolute URLs (https://...)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hero Slides Section -->
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-images me-2"></i>Hero Slides
                            <span class="badge ms-auto">Dynamic</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <p class="text-muted mb-0">Create multiple slides for your hero section carousel</p>
                            <button type="button" class="btn btn-primary" onclick="addSlide()">
                                <i class="fas fa-plus me-1"></i>Add Slide
                            </button>
                        </div>

                        <div id="slides-container">
                            <!-- Slides will be added here dynamically -->
                        </div>

                        <div class="text-center py-5" id="no-slides-message">
                            <div class="text-muted">
                                <i class="fas fa-images fa-4x mb-3 opacity-25"></i>
                                <h6>No slides created yet</h6>
                                <p class="mb-3">Add slides to create a dynamic hero section with multiple content panels</p>
                                <button type="button" class="btn btn-outline-primary" onclick="addSlide()">
                                    <i class="fas fa-plus me-1"></i>Create Your First Slide
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hero Settings Section -->
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-cogs me-2"></i>Hero Settings
                            <span class="badge ms-auto">Configuration</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-play-circle me-1"></i>Slideshow Controls</h6>
                                <div class="form-check form-switch mb-3">
                                    <input asp-for="EnableSlideshow" class="form-check-input" />
                                    <label asp-for="EnableSlideshow" class="form-check-label">
                                        <i class="fas fa-images me-1"></i>Enable Slideshow
                                    </label>
                                    <small class="form-text text-muted d-block">Allow multiple slides to rotate automatically</small>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input asp-for="AutoPlay" class="form-check-input" />
                                    <label asp-for="AutoPlay" class="form-check-label">
                                        <i class="fas fa-play me-1"></i>Auto Play
                                    </label>
                                    <small class="form-text text-muted d-block">Automatically advance slides</small>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input asp-for="ShowDots" class="form-check-input" />
                                    <label asp-for="ShowDots" class="form-check-label">
                                        <i class="fas fa-circle me-1"></i>Show Navigation Dots
                                    </label>
                                    <small class="form-text text-muted d-block">Display dot indicators for slide navigation</small>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input asp-for="ShowArrows" class="form-check-input" />
                                    <label asp-for="ShowArrows" class="form-check-label">
                                        <i class="fas fa-chevron-left me-1"></i>Show Navigation Arrows
                                    </label>
                                    <small class="form-text text-muted d-block">Display left/right arrow controls</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success mb-3"><i class="fas fa-sliders-h me-1"></i>Advanced Settings</h6>
                                <div class="form-group">
                                    <label asp-for="SlideshowSpeed" class="form-label">
                                        <i class="fas fa-clock me-1"></i>Slideshow Speed (milliseconds)
                                    </label>
                                    <input asp-for="SlideshowSpeed" class="form-control" type="number" min="1000" max="30000" step="1000" />
                                    <span asp-validation-for="SlideshowSpeed" class="text-danger"></span>
                                    <small class="form-text text-muted">Time between slide transitions (1000-30000ms)</small>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input asp-for="EnableFloatingElements" class="form-check-input" />
                                    <label asp-for="EnableFloatingElements" class="form-check-label">
                                        <i class="fas fa-magic me-1"></i>Enable Floating Elements
                                    </label>
                                    <small class="form-text text-muted d-block">Add animated floating icons and elements</small>
                                </div>
                                <div class="form-group">
                                    <label asp-for="FloatingElementsConfig" class="form-label">
                                        <i class="fas fa-code me-1"></i>Floating Elements Config (JSON)
                                    </label>
                                    <textarea asp-for="FloatingElementsConfig" class="form-control" rows="4"
                                              placeholder='{"elements": [{"icon": "fas fa-code", "position": "top-left"}]}'></textarea>
                                    <span asp-validation-for="FloatingElementsConfig" class="text-danger"></span>
                                    <small class="form-text text-muted">JSON configuration for floating elements</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-info-circle me-2"></i>Quick Guide
                        </h5>
                    </div>
                    <div class="section-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-1"></i>Tips for Great Hero Sections</h6>
                            <ul class="mb-0 small">
                                <li>Keep titles concise and impactful</li>
                                <li>Use high-quality images or videos</li>
                                <li>Include clear call-to-action buttons</li>
                                <li>Test on different screen sizes</li>
                                <li>Optimize images for web performance</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-1"></i>Important Notes</h6>
                            <ul class="mb-0 small">
                                <li>Only one hero section per page is recommended</li>
                                <li>Slides should have consistent dimensions</li>
                                <li>Use relative URLs for internal links</li>
                                <li>Test slideshow timing for user experience</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Action Bar -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="section-card">
                    <div class="section-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <a asp-action="Index" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-1"></i>Back to List
                                </a>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Create Hero Section
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Styles {
    <style>
        .section-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border-bottom: none;
        }

        .section-header h5 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .section-header .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            margin-left: auto;
        }

        .section-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease-in-out;
            font-size: 0.95rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 0.125rem 0.25rem rgba(102, 126, 234, 0.25);
            transition: all 0.15s ease-in-out;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.35);
        }

        .required {
            color: #dc3545;
            margin-left: 0.25rem;
        }

        .slide-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .slide-item .slide-header {
            display: flex;
            justify-content-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #dee2e6;
        }

        .slide-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .media-toggle {
            background: #e9ecef;
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin-bottom: 1rem;
        }

        .media-toggle .btn {
            border-radius: 0.25rem;
            border: none;
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
        }

        .media-toggle .btn.active {
            background: #667eea;
            color: white;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0.75rem;
        }

        .sticky-actions {
            position: sticky;
            top: 1rem;
            z-index: 100;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-bottom: 2rem;
        }

        /* Additional responsive styles */
        @@media (max-width: 768px) {
            .page-header {
                padding: 1.5rem 0;
            }

            .sticky-actions {
                position: relative;
                margin-bottom: 1rem;
            }

            .section-card .section-body {
                padding: 1rem;
            }
        }

        /* Animation for form sections */
        .section-card {
            animation: slideInUp 0.3s ease-out;
        }

        @@keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Upload button styles */
        .upload-btn {
            border-radius: 0 0.5rem 0.5rem 0;
            border-left: none;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease-in-out;
            white-space: nowrap;
        }

        .upload-btn:hover {
            background-color: #667eea;
            border-color: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .input-group .upload-btn {
            z-index: 3;
        }

        /* Responsive upload button */
        @@media (max-width: 576px) {
            .upload-btn {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .upload-btn i {
                margin: 0;
            }

            .input-group {
                flex-wrap: nowrap;
            }
        }
    </style>
}

@section Scripts {
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize CKEditor for all textareas with ckeditor class
            document.querySelectorAll('.ckeditor').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                                'bold', 'italic', 'underline', '|',
                                'alignment', '|',
                                'link', 'insertImage', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ]
                        },
                        fontSize: {
                            options: [9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72]
                        },
                        fontFamily: {
                            options: [
                                'default',
                                'Arial, Helvetica, sans-serif',
                                'Georgia, serif',
                                'Times New Roman, Times, serif',
                                'Verdana, Geneva, sans-serif',
                                'Inter, sans-serif',
                                'Poppins, sans-serif'
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
                        }
                    })
                    .then(editor => {
                        console.log('CKEditor initialized for:', textarea.name);
                    })
                    .catch(error => {
                        console.error('CKEditor initialization error:', error);
                    });
            });
        });

        // Slide management
        let slideIndex = 0;

        function addSlide() {
            const container = document.getElementById('slides-container');
            const noSlidesMessage = document.getElementById('no-slides-message');

            const slideHtml = `
                <div class="slide-item" data-slide-index="${slideIndex}">
                    <div class="slide-header">
                        <div class="d-flex align-items-center">
                            <div class="slide-number">${slideIndex + 1}</div>
                            <h6 class="mb-0 ms-3">Slide ${slideIndex + 1}</h6>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSlide(this)">
                            <i class="fas fa-trash me-1"></i>Remove
                        </button>
                    </div>

                    <input type="hidden" name="Slides[${slideIndex}].Id" value="0" />
                    <input type="hidden" name="Slides[${slideIndex}].DisplayOrder" value="${slideIndex + 1}" />
                    <input type="hidden" name="Slides[${slideIndex}].IsActive" value="true" />
                    <input type="hidden" name="Slides[${slideIndex}].AnimationType" value="fade" />
                    <input type="hidden" name="Slides[${slideIndex}].Duration" value="5000" />

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-edit me-1"></i>Slide Content<span class="required">*</span>
                        </label>
                        <textarea name="Slides[${slideIndex}].Content" class="form-control ckeditor-full" rows="6" required
                                  placeholder="Enter the content for this slide..."></textarea>
                        <small class="form-text text-muted">Use rich text formatting to create compelling slide content with titles, descriptions, and styling.</small>
                    </div>

                    <!-- Media Type Selection -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-photo-video me-1"></i>Media Type<span class="required">*</span>
                        </label>
                        <div class="media-toggle">
                            <button type="button" class="btn btn-sm active" onclick="setMediaType(${slideIndex}, 'image', this)">
                                <i class="fas fa-image me-1"></i>Image
                            </button>
                            <button type="button" class="btn btn-sm" onclick="setMediaType(${slideIndex}, 'video', this)">
                                <i class="fas fa-video me-1"></i>Video
                            </button>
                        </div>
                        <select name="Slides[${slideIndex}].MediaType" class="form-select d-none media-type-select" data-slide-index="${slideIndex}">
                            <option value="image" selected>Image</option>
                            <option value="video">Video</option>
                        </select>
                    </div>

                    <!-- Image Fields -->
                    <div class="media-fields image-fields" data-media-type="image" data-slide-index="${slideIndex}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-image me-1"></i>Image URL
                                    </label>
                                    <div class="input-group">
                                        <input name="Slides[${slideIndex}].ImageUrl" class="form-control"
                                               placeholder="https://example.com/image.jpg or /images/hero.jpg" />
                                        <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[${slideIndex}].ImageUrl">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    </div>
                                    <small class="form-text text-muted">URL to the slide background image or upload a new image</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-alt me-1"></i>Alt Text
                                    </label>
                                    <input name="Slides[${slideIndex}].MediaAlt" class="form-control"
                                           placeholder="Descriptive text for accessibility" />
                                    <small class="form-text text-muted">For screen readers and SEO</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Video Fields -->
                    <div class="media-fields video-fields" data-media-type="video" data-slide-index="${slideIndex}" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Video URL</label>
                                    <div class="input-group">
                                        <input name="Slides[${slideIndex}].VideoUrl" class="form-control" />
                                        <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Slides[${slideIndex}].VideoUrl">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Media Alt Text</label>
                                    <input name="Slides[${slideIndex}].MediaAlt" class="form-control" />
                                </div>
                            </div>
                        </div>

                        <!-- Video Settings -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input type="hidden" name="Slides[${slideIndex}].VideoAutoPlay" value="false" />
                                    <input name="Slides[${slideIndex}].VideoAutoPlay" class="form-check-input" type="checkbox" value="true" checked />
                                    <label class="form-check-label">Video Auto Play</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input type="hidden" name="Slides[${slideIndex}].VideoMuted" value="false" />
                                    <input name="Slides[${slideIndex}].VideoMuted" class="form-check-input" type="checkbox" value="true" checked />
                                    <label class="form-check-label">Video Muted</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input type="hidden" name="Slides[${slideIndex}].VideoLoop" value="false" />
                                    <input name="Slides[${slideIndex}].VideoLoop" class="form-check-input" type="checkbox" value="true" checked />
                                    <label class="form-check-label">Video Loop</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input type="hidden" name="Slides[${slideIndex}].VideoControls" value="false" />
                                    <input name="Slides[${slideIndex}].VideoControls" class="form-check-input" type="checkbox" value="true" />
                                    <label class="form-check-label">Video Controls</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Button Text</label>
                                <input name="Slides[${slideIndex}].ButtonText" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Button URL</label>
                                <input name="Slides[${slideIndex}].ButtonUrl" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Display Order</label>
                                <input name="Slides[${slideIndex}].DisplayOrder" class="form-control" type="number" min="1" value="${slideIndex + 1}" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Animation Type</label>
                                <select name="Slides[${slideIndex}].AnimationType" class="form-select">
                                    <option value="fade">Fade</option>
                                    <option value="slide">Slide</option>
                                    <option value="zoom">Zoom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Duration (ms)</label>
                                <input name="Slides[${slideIndex}].Duration" class="form-control" type="number" min="1000" max="30000" step="1000" value="5000" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input name="Slides[${slideIndex}].IsActive" class="form-check-input" type="checkbox" checked />
                                    <label class="form-check-label">Is Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', slideHtml);
            slideIndex++;
            updateSlideNumbers();
            initializeMediaTypeHandlers();

            // Hide no slides message
            if (noSlidesMessage) {
                noSlidesMessage.style.display = 'none';
            }

            // Initialize CKEditor for newly added textareas
            const newSlide = container.lastElementChild;
            newSlide.querySelectorAll('.ckeditor-full').forEach(function(textarea) {
                ClassicEditor
                    .create(textarea, {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                                'bold', 'italic', '|',
                                'alignment', '|',
                                'link', 'insertImage', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ],
                            shouldNotGroupWhenFull: true
                        },
                        fontSize: {
                            options: [
                                9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                            ]
                        },
                        fontFamily: {
                            options: [
                                'default',
                                'Arial, Helvetica, sans-serif',
                                'Courier New, Courier, monospace',
                                'Georgia, serif',
                                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                                'Tahoma, Geneva, sans-serif',
                                'Times New Roman, Times, serif',
                                'Trebuchet MS, Helvetica, sans-serif',
                                'Verdana, Geneva, sans-serif',
                                'Inter, sans-serif',
                                'Poppins, sans-serif',
                                'Lato, sans-serif'
                            ]
                        },
                        fontColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        fontBackgroundColor: {
                            colors: [
                                { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
                                { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
                                { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
                                { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
                                { color: 'hsl(0, 0%, 0%)', label: 'Black' },
                                { color: 'hsl(0, 75%, 60%)', label: 'Red' },
                                { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
                                { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
                                { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
                                { color: 'hsl(120, 75%, 60%)', label: 'Green' },
                                { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
                                { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
                                { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
                                { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
                                { color: 'hsl(270, 75%, 60%)', label: 'Purple' }
                            ]
                        },
                        language: 'en',
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        image: {
                            toolbar: [
                                'imageTextAlternative',
                                'imageStyle:inline',
                                'imageStyle:block',
                                'imageStyle:side'
                            ]
                        }
                    })
                    .then(editor => {
                        console.log('Full CKEditor initialized for new slide:', textarea.name);
                    })
                    .catch(error => {
                        console.error('Full CKEditor initialization error:', error);
                    });
            });
        }

        function removeSlide(button) {
            const slideItem = button.closest('.slide-item');
            const container = document.getElementById('slides-container');
            const noSlidesMessage = document.getElementById('no-slides-message');

            slideItem.remove();
            updateSlideNumbers();

            // Show no slides message if no slides left
            if (container.children.length === 0 && noSlidesMessage) {
                noSlidesMessage.style.display = 'block';
            }
        }

        function updateSlideNumbers() {
            const slides = document.querySelectorAll('.slide-item');
            slides.forEach((slide, index) => {
                const title = slide.querySelector('h6');
                const slideNumber = slide.querySelector('.slide-number');
                if (title) {
                    title.textContent = `Slide ${index + 1}`;
                }
                if (slideNumber) {
                    slideNumber.textContent = index + 1;
                }
            });
        }

        function setMediaType(slideIndex, mediaType, button) {
            // Update button states
            const buttons = button.parentElement.querySelectorAll('.btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Update hidden select
            const select = document.querySelector(`select[name="Slides[${slideIndex}].MediaType"]`);
            if (select) {
                select.value = mediaType;
            }

            // Show/hide appropriate fields
            const imageFields = document.querySelector(`.image-fields[data-slide-index="${slideIndex}"]`);
            const videoFields = document.querySelector(`.video-fields[data-slide-index="${slideIndex}"]`);

            if (mediaType === 'image') {
                imageFields.style.display = 'block';
                videoFields.style.display = 'none';
            } else {
                imageFields.style.display = 'none';
                videoFields.style.display = 'block';
            }
        }

        // File Upload Functionality
        function initializeFileUpload() {
            // Handle file upload buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.upload-btn')) {
                    const btn = e.target.closest('.upload-btn');
                    const targetField = btn.dataset.target;
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = targetField.includes('Video') ? 'video/*' : 'image/*';

                    const originalContent = btn.innerHTML;

                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (file) {
                            // Create FormData for file upload
                            const formData = new FormData();
                            formData.append('file', file);

                            // Show loading state
                            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                            btn.disabled = true;

                            // Upload file
                            fetch('/Admin/Settings/UploadFile', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Update the input field with the uploaded file URL
                                    const targetInput = document.querySelector(`input[name="${targetField}"]`);
                                    if (targetInput) {
                                        targetInput.value = data.url;
                                        // Trigger change event for validation
                                        targetInput.dispatchEvent(new Event('change'));
                                    }
                                } else {
                                    alert('Upload failed: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Upload error:', error);
                                alert('Upload failed. Please try again.');
                            })
                            .finally(() => {
                                // Reset button state
                                btn.innerHTML = originalContent;
                                btn.disabled = false;
                            });
                        }
                    };

                    input.click();
                }
            });
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Initialize file upload when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initializeFileUpload();
        });
    </script>
}
