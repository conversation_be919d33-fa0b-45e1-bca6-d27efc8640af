{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["m9y6bvzI5DzbjTZG1gdBEya73vYLdtCDiScK0iZpbSM=", "E5YjGXK1qJ5QtM6ARK9mOYLp3HNiRXW6n7P+gjhsSj4=", "1gxhq2SI2ZgSZeuOfcZa2T0zuQ1Rqz6LmWj2TsKXg1s=", "FtiJbfDy9m9AnQbHQCWK0k68k+wqhUNcaPrRgvWYMNM=", "9v1feAlOpaEpr/zaGkN05I930BlCs3yTtDVVCe+htks=", "EiURXyFPmC3p6L8nDVsNBNggHlawXYWkK9aO1XLQbH0=", "OtY2R93KvXKEqfWs6nEqvTzl6mjU7HEGEGr5rkyeCkE=", "ICkiHG34jGi3Edp9GfCcqGaBOqJuTANOLRb0xE/FH0g=", "7lUzImwNECVHJ4uWXOmXeGMSW9wMLRyTHmw6eNSZgw4=", "An1mhmT76zyViVwuTA2PAjHqRrKLysNCMwpQf8swcig=", "UJY1xXq+HMH5lprujiWeyAkh06YCwSSxT7D1C+fQfLo=", "X5SyEDMJY6+r3VjwY7sKV2fbagynG12aBbArk2Xq5wY=", "DgA4bu00bwcIHPT/4PKneju3FjNpRnpc+tPoVELe/Ak=", "5mdhX8ywC2JU5BUUgFQAI5OyUAObr9dVG1q0d6NHrUQ=", "m8GPUJbDjM0lSFNW6QfHoyOcOVxrw55DK7XzYHe9K38=", "BbrPpXDTTj/PWHdZU3PahRdmXb43nzDSj8VpWVeyoUk=", "a1tlx2s02e9yEfHiloriMIdiID5QZe2r3AQ/BmZqr90=", "NIjyVCNTXPYCd33FRceub2W9y64AwJwGYb1rAoueX3s=", "vUtlynyqU2qsL1mNkjD9ncgU6x74fm9ZkeHANlpmfRw=", "aEC1r+GQKi/zepWcMRDncgSh3blMN1H8Oe4HflCuEJc=", "UwxTmFjZreeJHVUWrgXta3nkcK08ukVXnGfzcGnyI2I=", "JWemiVUlk0BiA2dHQkRQ2TZt/zSXj96i0GNS1Henmyk=", "7WKgdggZwGVVcmWLbHhJiOQsY5ScGN+k72QQ8nM72Xs=", "q3Ny1beZtoMMl4tdz9tKGr54itpFDrOYfvit2R6TRpk=", "qgdccV/WGy4pZM89uBTX+FGi8lYIHZAYYsi5gef1jis=", "vju5X7CeARY+uTODxtJN1vaRlD1UfLolSO8iQ/hV6AQ=", "DoUx7V8wsOEM47X7HqS1iGTJbgZqnQcWaJ3yIyqxZD4=", "cZqU3Pziy720b9fUDJ9UDogNSGfeGc2Nknz3+3uLxGo=", "t3I53Rab2bmpDu5E+bgKxTVkyBTtQb/uP+pjZtS50vs=", "LiUmsYje/hjafnRsB5aVcXmVnYh9brkxp1cuNJQWysc=", "dAwVqjd2EwB7vG0UFuYQUxHyYOes56GcV8INmbRU3XY=", "FmwZxviF5zaU0PF+T6hJjouKkiWRKE2UVbvETGLekoU=", "Ic8/d7AWG+zIwOvEqKxqIkO9L5xWXio5TpRBKBSzb8w=", "+DMQ0NeklDX1j1AS2CS8wnG8mj/5pygK5suH2XEF1J8=", "nGyHFNvlIcej1VdG9TCgNsIfzFzGg5HPvQmOXZW1G4k=", "tICJQ5hlGRyhuAKvUOc39wwuLKwkKFj3vcpdLiBqjbY=", "y/6RSauCxBQw9cAs3coWcGhljmcYCnXxPWKbVWyzfd0=", "cKNXBZyrJsk0F9u/fhSZL8wqJJdWr3swi6RF7jYgtOk=", "fXl+ArX2YiUoaI+CZ1UO+KzgFKC0Ee/GoBf9mpfYGYU=", "56CRsmzGHYAvfrHGuwwfHmFnhRgAMPCDW4RXeB5wQEM=", "/v7JFGoT6nR5hYcf64LoyujJckeaahOArsXSLLlmFj8=", "P3XvsBVfz9LsTlkiTh/R2NQpnxsayowWUNcdzCVwojw=", "ElXeIhTux3rxOpLqeF0HujC4Ivzbp/iyb/sNySI9c3A=", "SGYsHFpjen2R9X/PsJob7cDsOEaVAMPy9/TqdguTOzU=", "J2eA0KhDR7GNgWHrHbYBXij47ID6+OVP7hnT0Mq24Qs=", "MTDfmSTXt+ZHEsyG+cCD9AdbKkxtc+cUxlo8j42xEHQ=", "lslZ3ZXzgdQEaNdTtGIK54M7s9P+6WWKifHZT6QTv5s=", "q8yAI5EJcQP2szOnq9zGZskj91V3GhP1x3f4GfaaOjE=", "Sva5lMSbM0KMdM/S3+cgOSyaYt9z8PuzbmDJuhLWweA=", "ZQ33ngCS2VG1q8t8A3+tptYYHF6+omF6vYAOFmGwNLU=", "8kL4PbErXxAboxtlXQ0iOxLwBUcPdGGJkLFhg2Yyro4=", "NM8QbvrdX4jaqHGNwQb4OhqxH7boBQr4vPtlihSTZVs=", "9xv1hdC9wVbelke1Hj3nsyfkC54sDoUo68qcCDi/wbI=", "MEUatUtNLIoZIBLlnOXcEP2scywoGfLYP9b4ruygnww=", "1Uw5zQrn19hVjTQY0lRUCvIA5Pfazo/PguwWQAbKl5c=", "t2FHz1buLu3OyHma46TDD4SQEwbAmWIAlvJQ307b+oE=", "W8+mUiwsOUNmHAfrOp7u7GG8194rbwkciPV3EiykOOc=", "kVqBF/g6+WKNeN/pVgpW53QPOBrgw9S4cndH5b2pSL8=", "AV/xmbNQz1VjXg4Q4IEMyPaD49B2vbLeE/h9e79wkqg=", "wPpWVBeXmS2tDyUGPfct0O17hfRp/m7fCKMZjLl1yn4=", "KDKblPnr7WoSu5hSm8f2FgAe9rw4mMDxeP/UEYqI3Fg=", "2kEDPY8bxo5jGJL5xP93gFxYYjsk/UezpwME47jF1JQ=", "M64nA+DYvLKzMMa0N8wFJtH8mhRgS7fnAWXIOFI48Bo=", "CiuhiOgC0/GMCi9rVGys27hqu9X1njiyKYFriAqjn4Y=", "SXGlSenGyfjZQAI0qscQUfphdA/jPf7gA3uT2aDHx94=", "H2kmauGEcnVLYhp+k7Zb49O6GNFrNQhhwpRbvmrCi58=", "frjlmF/GGy9AnxhQhS+T0jvfA5kJPsGEBMMdAyOvLak=", "6eFIZxIYrZl/8rtpgr37n+Q1tJLoAaC3jrAkoL2meyc=", "7TZDRCtlC748cFXcCa9WxuhB9OOs2ezvKmBT66urxF8=", "Dp2FKSzkFaRPcMppFyF1hSFSpsGc1wX1A08H68hh4Ws=", "XZAa/407+qOXaKsleThnFZA7R3pzZmHnXiUVEk9VhHI=", "5Hhv0Xe+cctSXCwuTN2DbuGDfHsXeookh7JeE0nPTNQ=", "xv9jV3zNcvIoZnbMcnopb/GFtMpbb3YEpKNG/BGgB1I=", "UzlHsXo8YiWlEB3cnWdUyVin59opzbWf3wcqKBZg48k=", "EgvaPgkMAG9dZWCRMEzAKa+waWGjp9rpVyjQl8vrTkI=", "yd52y7Qgainig/BVfh4RTG8dpWjsJcIG1FN4I0niZJo=", "FxeLmPL8/M/WgGdNwHoZKoIZlqYT1n+r9p1LnEGbhik=", "uET4kYSWfipRXrF13qWsovJcwF89uTZcSXSKMwVjJm8=", "0Q14Jo8RTpGruq0tps8UFvRlF+Z5La2t+0+2OqCgzBE=", "l08myFBaWVQXAUlWdt0oVP7lomTX8o4q32BCG4BIFTk=", "yoYBT0llZQKXpe9CgXAf3ocwoFMgK0i0qRS03erGDhQ=", "OTPKBk+9SOrQy1zxxvASgtRP+2VpAwwLpmNPjEG99Gc=", "Xl8gSAckaAx5+bnMWzM43KjFTrngqgTHSsfXSM3FDKI=", "3E+q5lfVsX2JGRGiiwQE0y8n0O6Az6d95JMG0qnH4bk=", "K8oyLyy8zPGTKppJhSWB1udtqByF9Bnq9NqzL7mQm3c=", "yjsSjy5MGKIoPvlSqDJt6hjAI337PQB1Py284xj2x38=", "H90ajrXl22arclCM1ssVcLgcFnFxW/Btr8HQrdP1J/E=", "Jo3w/JFVkmjeMp8eBpg7SGEckjHukSmm/WATipA2FpE=", "Q17sFQq5ucwkATvRknLRx8UMUbm6uOUy06I+reGrZO0=", "eV5d6Pj1s2DNifEA3WVSBUs0dM0jk3O2wW/ZNOqY8q8=", "nWlQzWc/NUwtl+lJ1+Etk2Pa1Kab/tHetLSDrKb1Qrk=", "Yk9D11XdPEeVj2vQOqikeC3+RVUD7AMwZs2xmXpiAxI=", "E9oTMvbvMAe7zC1Fc/OZbyS8U6RlcGFccILxz+StDYM=", "NzzmYoSQ2DXh9j/TdQ+aqaBc/AmJoNUNNlf4ezobjWY=", "zd/GDem3B15zMD65D8gNYCL5NHEsVyfvr7C2C7S6Pgc=", "7Ljdhx4mKxkYW0uEJvlDXV1iFcuMVOLd0Csn2JVoxNY=", "+BOndNtE/LwOMKvHwgc3niYuIe/9e2OV+IhU0KkQemg=", "deHWKQydx3+JmkvmtY3tLr6NFz+tAbNyEKn4ZV20G6Y=", "Z2KBr8gKddOP5hddlS6ePf+4UGMn2gHBbiqGSxUxwHo=", "bPSvvgKij+ERXgqSWyEzf719wzOsnD3FWpg/24OSrz8=", "ywLBuPhTlERGze91ZbaxlDaeuPWJRAsUsvVYOt2OU8o=", "qQIPvEaxEWCLLV7oz1HSjU5CtY+9c3XzZpOD3Y3CjXY=", "zCz/l7DLqwyzCOeS6nZKZbn4melRi/dv0x1nELBJqv0=", "gzfqmTU/bsyxemm+87kjMOropHXuLyDxm6kED81KFVQ=", "iV8PHe/qyOe83RJDJyGs9LcHVhZfm0meIPMChEgN5O8=", "xhrfacrilgtlVOWWhHcgbOtwcBe35nFQvNDYQNecN4Y=", "wklbz2op8XCF/KyI0Hm9CKqykNxkuyybSmRhhW90n/o=", "UKZgNOjv06gWLU8ZU8L1nBBF2cO1CXbEv0saSie5U24=", "1DeYYU3rnXc9f+XJGBzoYWqvwxSTB9O5r0JnavxyaEs=", "a1fx29lE/AzLcSPORSLK2A/f1j5TwC8neEtRK93Q9Iw=", "4nj3iBjNjc63l6bFOUSGEdPNJIASjwDq1+JqxiRE7qo=", "7g9N1v3Ep/Ga16KLJv8EMdVek6r6S0TPF4C2dS2MlUM=", "/Dkn6NkN/i94FFQ3UEAGg+VPUCJy5VgqqVMmM6E0jf0=", "Hy6SrW9qsdiEymZnCApE1j/giCbXFDewDcd17/YpUx8=", "m6+Nsd3XHsBXyO9qTkuHzhqBkNh3OCisRkObl/slUA4=", "opluku6qHhxWfMFuu+zEoLywZkPe87nvKL9ZjZLPWww=", "x1j2vCKIY8Dw+aOrMlYuVcioHKCUFCFpxBjS8TrZy2M=", "mXgKjXYdKWfLImH5WqM65/MiNV1xalbIcwL0MHjSAg8=", "YSUEl5itk1Xl+RqidMo/NRv0IXYOe1siYVBeR904vMU=", "o9TRSKEVBAmsEj+fh93DS4Lr+CwPCt/x2UbnXM6ekq0=", "J7M6wvTIJWwZo32rtyOzHrFJ0CgAEMJiTAMi3ZG6f40=", "dzOgh2gr48cO3NAsDkwUDV2uYuNgKXnjDjovO7sLUTg=", "izc2bRD1sDkpllihWhUZAnrWgLNms/Sc4IEp5jCSwHk=", "JOU1G4v6Vhq8NmWQDYj4aWLr++wdLfNOKOiIK2F5Cpk=", "sGkIs2wMdXaqnDNilL5/E0d2+ODmQQFCOz6sEXmEGD0=", "DglQGTrr5suMQZw3r1VQzhEuojlLrrmNcVOAnnu5Wlw=", "Njrimx0MqKRpTzwRoHGiohYKzcMgGxUjjz1yUoQRr98=", "toR2ncq+k2ViPhyBNACocu0zP8y6TrfWSZFiIDHcNXg=", "PVYkrtEdGSuF82ELRR2mEV1HjtlLTHq4syeIjjzrIXg=", "6POObeGSFqMzqEq7RQHNNnExd5jfQWd+XJAROiBOJcY=", "nZny5Wl+q2YoqfkuTorFzAKuw3dNLGB21De9baOYarA=", "k8zpqPhNr2lP5UuhfhdPuB5YhojUh2M59boGxfSaSAQ=", "iN/oiCR4VJcDQJvxtwK0w+j1tf/iKEbBYBQN8MNjwic=", "giZBnVEz9NhaJQNvJXAB+h0Dr8nNadSqULfULb0FHQU=", "YCHClbNfFyzb44djBpzrrzSEpL7JwAW5fdmC9of6m20=", "haaqJZ7MYnwZbrUREInfzErOnxXkRJI+xgBYnVvHawg=", "y45Cn2sBukCGlEkg/UhG9h381s9k1PUe3WpRSnFX4EQ=", "J3cyc+sk0XfUTLCKBkBCnSGdlRkC7eM32qi0yu96Buc=", "NQiiNEwYXgRo9whHszNEe8PuZB+tkRNTEMsIF0EKfNo=", "NmW9ssQiRgHTte8MdVkR68D4ES7sOK6OLNJ3l5WmASs=", "gTLskG6Eg4r0xrkQoioDkBiYGDwOu4DtGWfTObs7zgA="], "CachedAssets": {"m9y6bvzI5DzbjTZG1gdBEya73vYLdtCDiScK0iZpbSM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-05-30T14:51:19.7026311+00:00"}, "E5YjGXK1qJ5QtM6ARK9mOYLp3HNiRXW6n7P+gjhsSj4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-05-30T14:51:19.7046345+00:00"}, "1gxhq2SI2ZgSZeuOfcZa2T0zuQ1Rqz6LmWj2TsKXg1s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-05-30T14:51:19.7066387+00:00"}, "FtiJbfDy9m9AnQbHQCWK0k68k+wqhUNcaPrRgvWYMNM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-30T14:51:19.7092094+00:00"}, "9v1feAlOpaEpr/zaGkN05I930BlCs3yTtDVVCe+htks=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-30T14:51:19.7340906+00:00"}, "EiURXyFPmC3p6L8nDVsNBNggHlawXYWkK9aO1XLQbH0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-30T14:51:19.7419395+00:00"}, "OtY2R93KvXKEqfWs6nEqvTzl6mjU7HEGEGr5rkyeCkE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-30T14:51:19.7459469+00:00"}, "ICkiHG34jGi3Edp9GfCcqGaBOqJuTANOLRb0xE/FH0g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-30T14:51:19.7399348+00:00"}, "7lUzImwNECVHJ4uWXOmXeGMSW9wMLRyTHmw6eNSZgw4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-30T14:51:19.7439437+00:00"}, "An1mhmT76zyViVwuTA2PAjHqRrKLysNCMwpQf8swcig=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-30T14:51:19.7479519+00:00"}, "UJY1xXq+HMH5lprujiWeyAkh06YCwSSxT7D1C+fQfLo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-30T14:51:19.7807591+00:00"}, "X5SyEDMJY6+r3VjwY7sKV2fbagynG12aBbArk2Xq5wY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-30T14:51:19.7867693+00:00"}, "DgA4bu00bwcIHPT/4PKneju3FjNpRnpc+tPoVELe/Ak=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-30T14:51:19.7921332+00:00"}, "5mdhX8ywC2JU5BUUgFQAI5OyUAObr9dVG1q0d6NHrUQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-30T14:51:19.7951689+00:00"}, "m8GPUJbDjM0lSFNW6QfHoyOcOVxrw55DK7XzYHe9K38=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-30T14:51:20.3974447+00:00"}, "BbrPpXDTTj/PWHdZU3PahRdmXb43nzDSj8VpWVeyoUk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-30T14:51:19.7867693+00:00"}, "a1tlx2s02e9yEfHiloriMIdiID5QZe2r3AQ/BmZqr90=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-30T14:51:19.7921332+00:00"}, "NIjyVCNTXPYCd33FRceub2W9y64AwJwGYb1rAoueX3s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-30T14:51:19.7479519+00:00"}, "vUtlynyqU2qsL1mNkjD9ncgU6x74fm9ZkeHANlpmfRw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-30T14:51:19.7911332+00:00"}, "aEC1r+GQKi/zepWcMRDncgSh3blMN1H8Oe4HflCuEJc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-30T14:51:19.7951689+00:00"}, "UwxTmFjZreeJHVUWrgXta3nkcK08ukVXnGfzcGnyI2I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-30T14:51:19.8489469+00:00"}, "JWemiVUlk0BiA2dHQkRQ2TZt/zSXj96i0GNS1Henmyk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-30T14:51:20.4004436+00:00"}, "7WKgdggZwGVVcmWLbHhJiOQsY5ScGN+k72QQ8nM72Xs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-30T14:51:20.405271+00:00"}, "q3Ny1beZtoMMl4tdz9tKGr54itpFDrOYfvit2R6TRpk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-30T14:51:20.4271026+00:00"}, "qgdccV/WGy4pZM89uBTX+FGi8lYIHZAYYsi5gef1jis=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-30T14:51:20.4311127+00:00"}, "vju5X7CeARY+uTODxtJN1vaRlD1UfLolSO8iQ/hV6AQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-30T14:51:20.435123+00:00"}, "DoUx7V8wsOEM47X7HqS1iGTJbgZqnQcWaJ3yIyqxZD4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-30T14:51:20.4455149+00:00"}, "cZqU3Pziy720b9fUDJ9UDogNSGfeGc2Nknz3+3uLxGo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-30T14:51:20.4535434+00:00"}, "t3I53Rab2bmpDu5E+bgKxTVkyBTtQb/uP+pjZtS50vs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-30T14:51:20.4733583+00:00"}, "LiUmsYje/hjafnRsB5aVcXmVnYh9brkxp1cuNJQWysc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-30T14:51:20.4827753+00:00"}, "dAwVqjd2EwB7vG0UFuYQUxHyYOes56GcV8INmbRU3XY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-30T14:51:20.4916263+00:00"}, "FmwZxviF5zaU0PF+T6hJjouKkiWRKE2UVbvETGLekoU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-30T14:51:20.4995205+00:00"}, "Ic8/d7AWG+zIwOvEqKxqIkO9L5xWXio5TpRBKBSzb8w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-30T14:51:20.5205676+00:00"}, "+DMQ0NeklDX1j1AS2CS8wnG8mj/5pygK5suH2XEF1J8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-30T14:51:20.5263167+00:00"}, "nGyHFNvlIcej1VdG9TCgNsIfzFzGg5HPvQmOXZW1G4k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-30T14:51:19.5326905+00:00"}, "tICJQ5hlGRyhuAKvUOc39wwuLKwkKFj3vcpdLiBqjbY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-30T14:51:19.7086436+00:00"}, "y/6RSauCxBQw9cAs3coWcGhljmcYCnXxPWKbVWyzfd0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-30T14:51:19.7379272+00:00"}, "cKNXBZyrJsk0F9u/fhSZL8wqJJdWr3swi6RF7jYgtOk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-30T14:51:19.7459469+00:00"}, "fXl+ArX2YiUoaI+CZ1UO+KzgFKC0Ee/GoBf9mpfYGYU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-30T14:51:19.7549695+00:00"}, "56CRsmzGHYAvfrHGuwwfHmFnhRgAMPCDW4RXeB5wQEM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-30T14:51:19.8001146+00:00"}, "/v7JFGoT6nR5hYcf64LoyujJckeaahOArsXSLLlmFj8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-30T14:51:20.391131+00:00"}, "P3XvsBVfz9LsTlkiTh/R2NQpnxsayowWUNcdzCVwojw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-30T14:51:20.3974447+00:00"}, "ElXeIhTux3rxOpLqeF0HujC4Ivzbp/iyb/sNySI9c3A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-30T14:51:20.4034949+00:00"}, "SGYsHFpjen2R9X/PsJob7cDsOEaVAMPy9/TqdguTOzU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-30T14:51:20.4271026+00:00"}, "J2eA0KhDR7GNgWHrHbYBXij47ID6+OVP7hnT0Mq24Qs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-30T14:51:20.4394618+00:00"}, "MTDfmSTXt+ZHEsyG+cCD9AdbKkxtc+cUxlo8j42xEHQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-30T14:51:20.3964459+00:00"}, "lslZ3ZXzgdQEaNdTtGIK54M7s9P+6WWKifHZT6QTv5s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-30T14:51:20.4014464+00:00"}, "q8yAI5EJcQP2szOnq9zGZskj91V3GhP1x3f4GfaaOjE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-30T14:51:20.405271+00:00"}, "Sva5lMSbM0KMdM/S3+cgOSyaYt9z8PuzbmDJuhLWweA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-30T14:51:20.4250963+00:00"}, "ZQ33ngCS2VG1q8t8A3+tptYYHF6+omF6vYAOFmGwNLU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-30T14:51:20.4271026+00:00"}, "8kL4PbErXxAboxtlXQ0iOxLwBUcPdGGJkLFhg2Yyro4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-30T14:51:20.4291077+00:00"}, "NM8QbvrdX4jaqHGNwQb4OhqxH7boBQr4vPtlihSTZVs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-30T14:51:19.7026311+00:00"}, "9xv1hdC9wVbelke1Hj3nsyfkC54sDoUo68qcCDi/wbI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-30T14:51:19.7479519+00:00"}, "MEUatUtNLIoZIBLlnOXcEP2scywoGfLYP9b4ruygnww=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-30T14:51:19.7888762+00:00"}, "1Uw5zQrn19hVjTQY0lRUCvIA5Pfazo/PguwWQAbKl5c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-30T14:51:19.7931324+00:00"}, "t2FHz1buLu3OyHma46TDD4SQEwbAmWIAlvJQ307b+oE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-30T14:51:19.8509514+00:00"}, "W8+mUiwsOUNmHAfrOp7u7GG8194rbwkciPV3EiykOOc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-30T14:51:20.4535434+00:00"}, "kVqBF/g6+WKNeN/pVgpW53QPOBrgw9S4cndH5b2pSL8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-30T14:51:20.4593087+00:00"}, "AV/xmbNQz1VjXg4Q4IEMyPaD49B2vbLeE/h9e79wkqg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-30T14:51:20.443509+00:00"}, "wPpWVBeXmS2tDyUGPfct0O17hfRp/m7fCKMZjLl1yn4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-30T14:51:20.4475197+00:00"}, "KDKblPnr7WoSu5hSm8f2FgAe9rw4mMDxeP/UEYqI3Fg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-30T14:51:20.4552918+00:00"}, "2kEDPY8bxo5jGJL5xP93gFxYYjsk/UezpwME47jF1JQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-30T14:51:20.4573009+00:00"}, "M64nA+DYvLKzMMa0N8wFJtH8mhRgS7fnAWXIOFI48Bo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ngr7uwcar-uvew6zmn7y.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin#[.{fingerprint=uvew6zmn7y}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9sb3bg5f39", "Integrity": "aZmu649ce/hxkllcDVgMN4pnkJZH0EDw9b1T4xALNHo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\admin.css", "FileLength": 2795, "LastWriteTime": "2025-05-30T14:51:20.4573009+00:00"}, "CiuhiOgC0/GMCi9rVGys27hqu9X1njiyKYFriAqjn4Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pn3zjfqylx-g07bnx142d.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client#[.{fingerprint=g07bnx142d}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\client.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u372l94rk3", "Integrity": "hnZ3ayaGZFLAG6FjKMU6gjkmJLNka/tR9IAAQRpSPoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\client.css", "FileLength": 4346, "LastWriteTime": "2025-05-30T14:51:20.4593087+00:00"}, "SXGlSenGyfjZQAI0qscQUfphdA/jPf7gA3uT2aDHx94=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\elvx5emtjm-wilnnc3w1m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom#[.{fingerprint=wilnnc3w1m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k58f1us78f", "Integrity": "CDABZ8Got8gWH2Wic8T6MC1izVu156P7c25rWmLiqLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\custom.css", "FileLength": 212, "LastWriteTime": "2025-05-30T14:51:20.4593087+00:00"}, "H2kmauGEcnVLYhp+k7Zb49O6GNFrNQhhwpRbvmrCi58=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypbfssc6px-1edbnb0gu6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow#[.{fingerprint=1edbnb0gu6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kg1721j3l3", "Integrity": "3CYDHawx/sbpzjIQoN7+wnjWSSXtjNUj7txCQ3kydJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "FileLength": 1718, "LastWriteTime": "2025-05-30T14:51:20.4593087+00:00"}, "frjlmF/GGy9AnxhQhS+T0jvfA5kJPsGEBMMdAyOvLak=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xtt04yhxc6-z0oxv65qqt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage#[.{fingerprint=z0oxv65qqt}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8f892lsdcx", "Integrity": "HLcn/vkafXzbY1fT/tep63CZkPI+ruuC0QqtN3b9d/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "FileLength": 14915, "LastWriteTime": "2025-05-30T14:51:20.4613131+00:00"}, "6eFIZxIYrZl/8rtpgr37n+Q1tJLoAaC3jrAkoL2meyc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7ejlrje5jg-8znpj9knio.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site#[.{fingerprint=8znpj9knio}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tb45argtbf", "Integrity": "KXsoPJcXUP9jdH3J4w7jvn/bvOC4Ia8KvN2KNFAyres=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\css\\site.css", "FileLength": 2281, "LastWriteTime": "2025-05-30T14:51:20.4633196+00:00"}, "7TZDRCtlC748cFXcCa9WxuhB9OOs2ezvKmBT66urxF8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\03l1rte6gq-61n19gt1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-05-30T14:51:19.5248042+00:00"}, "Dp2FKSzkFaRPcMppFyF1hSFSpsGc1wX1A08H68hh4Ws=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ktm00ucnju-wgrwvlfr5s.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi#[.{fingerprint=wgrwvlfr5s}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkbigiuy64", "Integrity": "HQM1ysqWHmUnuAlp3aK0g+H3KTdRrGOY3H0Vq22/RWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "FileLength": 1935, "LastWriteTime": "2025-05-30T14:51:19.7310264+00:00"}, "XZAa/407+qOXaKsleThnFZA7R3pzZmHnXiUVEk9VhHI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qveyjng0ys-nedvdbx254.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell#[.{fingerprint=nedvdbx254}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edsoh1itdo", "Integrity": "olpJlaPKyLgPjxsEUdPEwIwMpvYmE5KFaI68j+OJmJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "FileLength": 336, "LastWriteTime": "2025-05-30T14:51:19.732091+00:00"}, "5Hhv0Xe+cctSXCwuTN2DbuGDfHsXeookh7JeE0nPTNQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ikbcgss9f7-k8cq9l2kib.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs#[.{fingerprint=k8cq9l2kib}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpoc1bcrbp", "Integrity": "GTXsRzccKflDEkzr6cT5Iq+5KyyN46VgzOgbjaIisWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "FileLength": 721, "LastWriteTime": "2025-05-30T14:51:19.732091+00:00"}, "xv9jV3zNcvIoZnbMcnopb/GFtMpbb3YEpKNG/BGgB1I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nnketcjwya-1x8wi64s97.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python#[.{fingerprint=1x8wi64s97}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6yyh4b7nm", "Integrity": "i5K/+SeOT5dWe0FWBa0GIm1JLWXKxGhZVNxsYkLDrns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "FileLength": 734, "LastWriteTime": "2025-05-30T14:51:19.7330902+00:00"}, "UzlHsXo8YiWlEB3cnWdUyVin59opzbWf3wcqKBZg48k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uham8ign5j-emsbkume29.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift#[.{fingerprint=emsbkume29}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvaavaxrke", "Integrity": "afO1CiL+LYXrmSV0hG/nzRY58iy/trDc+maeMmc1q8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "FileLength": 619, "LastWriteTime": "2025-05-30T14:51:19.7399348+00:00"}, "EgvaPgkMAG9dZWCRMEzAKa+waWGjp9rpVyjQl8vrTkI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\10owx4zk9i-y36mr8d3re.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo#[.{fingerprint=y36mr8d3re}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78ry7ld12l", "Integrity": "qVZsciNX4bvJyG/NFAFG6/1bWRdPLgFZLpfMSEqLyZo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "FileLength": 110221, "LastWriteTime": "2025-05-30T14:51:19.7509604+00:00"}, "yd52y7Qgainig/BVfh4RTG8dpWjsJcIG1FN4I0niZJo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\02bln8vcid-sixrurlrd9.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin#[.{fingerprint=sixrurlrd9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uqr37agqpc", "Integrity": "Nv7cEHicbCdluP+2jcL/v2G42iG+07cO6IUWbvPHzY0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\admin.js", "FileLength": 2012, "LastWriteTime": "2025-05-30T14:51:19.7847644+00:00"}, "FxeLmPL8/M/WgGdNwHoZKoIZlqYT1n+r9p1LnEGbhik=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v8u4sxh3fr-3xu3gfddrx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client#[.{fingerprint=3xu3gfddrx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\client.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkcwqz3pwi", "Integrity": "pD2v2NXmu8dIQdlAvDxSQC28LtmTLNKzC22UlumYprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\client.js", "FileLength": 390, "LastWriteTime": "2025-05-30T14:51:19.7847644+00:00"}, "uET4kYSWfipRXrF13qWsovJcwF89uTZcSXSKMwVjJm8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h4d7zuu3lf-rd81zyuzir.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow#[.{fingerprint=rd81zyuzir}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lq72lcfcg", "Integrity": "pkfBPs6ownzjPebnXD0fcxTJYLLrd3SJgTGnCXXCxXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "FileLength": 1678, "LastWriteTime": "2025-05-30T14:51:19.7888762+00:00"}, "0Q14Jo8RTpGruq0tps8UFvRlF+Z5La2t+0+2OqCgzBE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\br1wthuj05-wi7ss39h6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations#[.{fingerprint=wi7ss39h6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iftq384gbj", "Integrity": "gqWjPa9AQ/+yPWfZCbXLhtYUgjFs8Oxn+qn1YXdxUNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "FileLength": 2761, "LastWriteTime": "2025-05-30T14:51:19.7931324+00:00"}, "l08myFBaWVQXAUlWdt0oVP7lomTX8o4q32BCG4BIFTk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\5eaaj8jli5-2svxw61t86.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site#[.{fingerprint=2svxw61t86}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o75gd3adie", "Integrity": "ZHcCB5qku88L0iD5sq5euDqrhPm9N+4MeMk/tRrrgIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\js\\site.js", "FileLength": 769, "LastWriteTime": "2025-05-30T14:51:19.8469423+00:00"}, "yoYBT0llZQKXpe9CgXAf3ocwoFMgK0i0qRS03erGDhQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\huncoxf4nc-bqjiyaj88i.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-30T14:51:20.3448334+00:00"}, "OTPKBk+9SOrQy1zxxvASgtRP+2VpAwwLpmNPjEG99Gc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2jnr46nmpc-c2jlpeoesf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-30T14:51:20.3497109+00:00"}, "Xl8gSAckaAx5+bnMWzM43KjFTrngqgTHSsfXSM3FDKI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wmzyo7xuhb-erw9l3u2r3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-30T14:51:20.3931332+00:00"}, "3E+q5lfVsX2JGRGiiwQE0y8n0O6Az6d95JMG0qnH4bk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\djyxqsu5pv-aexeepp0ev.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-30T14:51:20.3974447+00:00"}, "K8oyLyy8zPGTKppJhSWB1udtqByF9Bnq9NqzL7mQm3c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2sbuxwf498-d7shbmvgxk.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-30T14:51:20.3994447+00:00"}, "yjsSjy5MGKIoPvlSqDJt6hjAI337PQB1Py284xj2x38=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fe676pkbci-ausgxo2sd3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-30T14:51:19.7439437+00:00"}, "H90ajrXl22arclCM1ssVcLgcFnFxW/Btr8HQrdP1J/E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2n44t9y1oe-k8d9w2qqmf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-30T14:51:19.7479519+00:00"}, "Jo3w/JFVkmjeMp8eBpg7SGEckjHukSmm/WATipA2FpE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2yiqndbjob-cosvhxvwiu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-30T14:51:19.7827619+00:00"}, "Q17sFQq5ucwkATvRknLRx8UMUbm6uOUy06I+reGrZO0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6laymexpp1-ub07r2b239.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-30T14:51:19.7867693+00:00"}, "eV5d6Pj1s2DNifEA3WVSBUs0dM0jk3O2wW/ZNOqY8q8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e23n28kbl6-fvhpjtyr6v.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-30T14:51:19.7931324+00:00"}, "nWlQzWc/NUwtl+lJ1+Etk2Pa1Kab/tHetLSDrKb1Qrk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0xdmrg7j3x-b7pk76d08c.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-30T14:51:19.8449375+00:00"}, "Yk9D11XdPEeVj2vQOqikeC3+RVUD7AMwZs2xmXpiAxI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7msow90u6m-fsbi9cje9m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-30T14:51:20.398445+00:00"}, "E9oTMvbvMAe7zC1Fc/OZbyS8U6RlcGFccILxz+StDYM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\y6lzeo23io-rzd6atqjts.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-30T14:51:20.4034949+00:00"}, "NzzmYoSQ2DXh9j/TdQ+aqaBc/AmJoNUNNlf4ezobjWY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b1eq4g63qz-ee0r1s7dh0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-30T14:51:20.4093219+00:00"}, "zd/GDem3B15zMD65D8gNYCL5NHEsVyfvr7C2C7S6Pgc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q7xywl88yg-dxx9fxp4il.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-30T14:51:20.4271026+00:00"}, "7Ljdhx4mKxkYW0uEJvlDXV1iFcuMVOLd0Csn2JVoxNY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tl4yfr4qca-jd9uben2k1.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-30T14:51:20.4291077+00:00"}, "+BOndNtE/LwOMKvHwgc3niYuIe/9e2OV+IhU0KkQemg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xb1bhl2rn-khv3u5hwcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-30T14:51:20.4331166+00:00"}, "deHWKQydx3+JmkvmtY3tLr6NFz+tAbNyEKn4ZV20G6Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1ju4p3k34w-r4e9w2rdcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-30T14:51:20.4394618+00:00"}, "Z2KBr8gKddOP5hddlS6ePf+4UGMn2gHBbiqGSxUxwHo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\jyl4075mxi-lcd1t2u6c8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-30T14:51:20.4455149+00:00"}, "bPSvvgKij+ERXgqSWyEzf719wzOsnD3FWpg/24OSrz8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zul1z3i66m-c2oey78nd0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-30T14:51:20.4535434+00:00"}, "ywLBuPhTlERGze91ZbaxlDaeuPWJRAsUsvVYOt2OU8o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tj8j7uiu1e-tdbxkamptv.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-30T14:51:20.4014464+00:00"}, "qQIPvEaxEWCLLV7oz1HSjU5CtY+9c3XzZpOD3Y3CjXY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l6ou9js1o2-j5mq2jizvt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-30T14:51:20.4133287+00:00"}, "zCz/l7DLqwyzCOeS6nZKZbn4melRi/dv0x1nELBJqv0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z3s3pqtpk8-06098lyss8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-30T14:51:19.7046345+00:00"}, "gzfqmTU/bsyxemm+87kjMOropHXuLyDxm6kED81KFVQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pkmk90gfad-nvvlpmu67g.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-30T14:51:19.7066387+00:00"}, "iV8PHe/qyOe83RJDJyGs9LcHVhZfm0meIPMChEgN5O8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zktux6y5y4-s35ty4nyc5.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-30T14:51:19.7310264+00:00"}, "xhrfacrilgtlVOWWhHcgbOtwcBe35nFQvNDYQNecN4Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\26o4et715b-pj5nd1wqec.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-30T14:51:19.7439437+00:00"}, "wklbz2op8XCF/KyI0Hm9CKqykNxkuyybSmRhhW90n/o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7gp52xdync-46ein0sx1k.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-30T14:51:19.7509604+00:00"}, "UKZgNOjv06gWLU8ZU8L1nBBF2cO1CXbEv0saSie5U24=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e7b6u3x55w-v0zj4ognzu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-30T14:51:19.7901337+00:00"}, "1DeYYU3rnXc9f+XJGBzoYWqvwxSTB9O5r0JnavxyaEs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9zg9c6chb7-37tfw0ft22.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-30T14:51:19.7960759+00:00"}, "a1fx29lE/AzLcSPORSLK2A/f1j5TwC8neEtRK93Q9Iw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\33czbugvzg-hrwsygsryq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-30T14:51:20.374203+00:00"}, "4nj3iBjNjc63l6bFOUSGEdPNJIASjwDq1+JqxiRE7qo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2d5epgijjo-pk9g2wxc8p.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-30T14:51:20.398445+00:00"}, "7g9N1v3Ep/Ga16KLJv8EMdVek6r6S0TPF4C2dS2MlUM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t63suxyd54-ft3s53vfgj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-30T14:51:20.4193411+00:00"}, "/Dkn6NkN/i94FFQ3UEAGg+VPUCJy5VgqqVMmM6E0jf0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5r96tufml-6cfz1n2cew.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-30T14:51:20.4311127+00:00"}, "Hy6SrW9qsdiEymZnCApE1j/giCbXFDewDcd17/YpUx8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wegvxza2ge-6pdc2jztkx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-30T14:51:20.4455149+00:00"}, "m6+Nsd3XHsBXyO9qTkuHzhqBkNh3OCisRkObl/slUA4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g09mkto0sq-493y06b0oq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-30T14:51:20.4495257+00:00"}, "opluku6qHhxWfMFuu+zEoLywZkPe87nvKL9ZjZLPWww=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4mg52w1lo3-iovd86k7lj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-30T14:51:20.4593087+00:00"}, "x1j2vCKIY8Dw+aOrMlYuVcioHKCUFCFpxBjS8TrZy2M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lh2isbcsad-vr1egmr9el.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-30T14:51:20.4613131+00:00"}, "mXgKjXYdKWfLImH5WqM65/MiNV1xalbIcwL0MHjSAg8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g1ftby5py3-kbrnm935zg.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-30T14:51:20.4331166+00:00"}, "YSUEl5itk1Xl+RqidMo/NRv0IXYOe1siYVBeR904vMU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vphg83v0d2-jj8uyg4cgr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-30T14:51:20.4455149+00:00"}, "o9TRSKEVBAmsEj+fh93DS4Lr+CwPCt/x2UbnXM6ekq0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hd7xpsd4po-y7v9cxd14o.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-30T14:51:19.7086436+00:00"}, "J7M6wvTIJWwZo32rtyOzHrFJ0CgAEMJiTAMi3ZG6f40=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2626qsfhg1-notf2xhcfb.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-30T14:51:19.7340906+00:00"}, "dzOgh2gr48cO3NAsDkwUDV2uYuNgKXnjDjovO7sLUTg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8c68735sf-h1s4sie4z3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-30T14:51:19.7479519+00:00"}, "izc2bRD1sDkpllihWhUZAnrWgLNms/Sc4IEp5jCSwHk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fij7bwzp8x-63fj8s7r0e.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-30T14:51:19.7876632+00:00"}, "JOU1G4v6Vhq8NmWQDYj4aWLr++wdLfNOKOiIK2F5Cpk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cji4aywtqv-0j3bgjxly4.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-30T14:51:19.7960759+00:00"}, "sGkIs2wMdXaqnDNilL5/E0d2+ODmQQFCOz6sEXmEGD0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9mvculopuj-47otxtyo56.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-30T14:51:20.3974447+00:00"}, "DglQGTrr5suMQZw3r1VQzhEuojlLrrmNcVOAnnu5Wlw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bf5okwooxo-4v8eqarkd7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-30T14:51:20.4004436+00:00"}, "Njrimx0MqKRpTzwRoHGiohYKzcMgGxUjjz1yUoQRr98=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g10371glbs-356vix0kms.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-30T14:51:20.4014464+00:00"}, "toR2ncq+k2ViPhyBNACocu0zP8y6TrfWSZFiIDHcNXg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2iu5gkm6iv-83jwlth58m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-30T14:51:20.4034949+00:00"}, "PVYkrtEdGSuF82ELRR2mEV1HjtlLTHq4syeIjjzrIXg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ahn74umtfx-mrlpezrjn3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-30T14:51:20.4271026+00:00"}, "6POObeGSFqMzqEq7RQHNNnExd5jfQWd+XJAROiBOJcY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8k9meis7w4-lzl9nlhx6b.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-30T14:51:20.4291077+00:00"}, "nZny5Wl+q2YoqfkuTorFzAKuw3dNLGB21De9baOYarA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6jty019ari-ag7o75518u.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-30T14:51:20.4311127+00:00"}, "k8zpqPhNr2lP5UuhfhdPuB5YhojUh2M59boGxfSaSAQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5qk1pgd3r-x0q3zqp4vz.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-30T14:51:20.4331166+00:00"}, "iN/oiCR4VJcDQJvxtwK0w+j1tf/iKEbBYBQN8MNjwic=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\64rjhqzavh-0i3buxo5is.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-30T14:51:20.4495257+00:00"}, "giZBnVEz9NhaJQNvJXAB+h0Dr8nNadSqULfULb0FHQU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\dmzx9f6yhi-o1o13a6vjx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-30T14:51:20.4535434+00:00"}, "YCHClbNfFyzb44djBpzrrzSEpL7JwAW5fdmC9of6m20=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zydbwp8g7y-ttgo8qnofa.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-30T14:51:20.4368712+00:00"}, "haaqJZ7MYnwZbrUREInfzErOnxXkRJI+xgBYnVvHawg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tivk7iw1cd-2z0ns9nrw6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-30T14:51:20.4633196+00:00"}, "y45Cn2sBukCGlEkg/UhG9h381s9k1PUe3WpRSnFX4EQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2v4zxome5j-muycvpuwrr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-30T14:51:19.7066387+00:00"}, "J3cyc+sk0XfUTLCKBkBCnSGdlRkC7eM32qi0yu96Buc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e15jphxp1u-87fc7y1x7t.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-30T14:51:19.7132519+00:00"}, "NQiiNEwYXgRo9whHszNEe8PuZB+tkRNTEMsIF0EKfNo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ezk5r3swy-mlv21k5csn.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-30T14:51:19.7310264+00:00"}, "NmW9ssQiRgHTte8MdVkR68D4ES7sOK6OLNJ3l5WmASs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ot0u7hstz0-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "FileLength": 545, "LastWriteTime": "2025-05-30T14:51:19.732091+00:00"}, "gTLskG6Eg4r0xrkQoioDkBiYGDwOu4DtGWfTObs7zgA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\on9a315cdm-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (5.29.25 - 6PM) - Copy - to work in it\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-05-30T14:51:19.7330902+00:00"}}, "CachedCopyCandidates": {}}