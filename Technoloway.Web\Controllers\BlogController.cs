using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class BlogController : Controller
{
    private readonly IRepository<BlogPost> _blogPostRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public BlogController(IRepository<BlogPost> blogPostRepository, IHeroSectionRepository heroSectionRepository)
    {
        _blogPostRepository = blogPostRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index(int page = 1, string category = null)
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Blog");
        int pageSize = 6;

        var query = _blogPostRepository.GetAllAsNoTracking()
            .Where(b => b.IsPublished && !b.<PERSON>);

        if (!string.IsNullOrEmpty(category))
        {
            query = query.Where(b => b.Categories.Contains(category));
        }

        var totalPosts = await query.CountAsync();
        var totalPages = Math.Max(1, (int)Math.Ceiling(totalPosts / (double)pageSize));

        page = Math.Max(1, Math.Min(page, totalPages));

        var posts = await query
            .OrderByDescending(b => b.PublishedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var viewModel = new BlogIndexViewModel
        {
            Posts = posts,
            CurrentPage = page,
            TotalPages = totalPages,
            Category = category
        };

        ViewBag.HeroSection = heroSection;
        return View(viewModel);
    }

    public async Task<IActionResult> Details(string slug)
    {
        if (string.IsNullOrEmpty(slug))
        {
            return NotFound();
        }

        var post = await _blogPostRepository.GetAllAsNoTracking()
            .FirstOrDefaultAsync(b => b.Slug == slug && b.IsPublished && !b.IsDeleted);

        if (post == null)
        {
            return NotFound();
        }

        // Get related posts
        var relatedPosts = await _blogPostRepository.GetAllAsNoTracking()
            .Where(b => b.IsPublished && !b.IsDeleted && b.Id != post.Id)
            .OrderByDescending(b => b.PublishedAt)
            .Take(3)
            .ToListAsync();

        var viewModel = new BlogDetailsViewModel
        {
            Post = post,
            RelatedPosts = relatedPosts
        };

        return View(viewModel);
    }
}
