@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Hero Section Details";
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <style>
        .section-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border-bottom: none;
        }

        .section-header h5 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .section-header .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            margin-left: auto;
        }

        .section-body {
            padding: 1.5rem;
        }

        .detail-item {
            margin-bottom: 1.25rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .detail-value {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .badge-status {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 500;
        }

        .slide-card {
            border: 1px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background: #f8f9fa;
            transition: all 0.15s ease-in-out;
        }

        .slide-card:hover {
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .slide-card:last-child {
            margin-bottom: 0;
        }

        .media-preview {
            max-width: 250px;
            max-height: 150px;
            border-radius: 0.5rem;
            object-fit: cover;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
        }

        .slide-content-preview {
            max-height: 200px;
            overflow: hidden;
            position: relative;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid #e9ecef;
        }

        .slide-content-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(transparent, white);
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0.75rem;
        }

        .status-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-bottom: 2rem;
        }

        .action-buttons {
            position: sticky;
            top: 1rem;
            z-index: 100;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .slide-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }

        .content-preview {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            margin-top: 1rem;
        }

        .button-preview {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .button-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .button-secondary {
            background: #6c757d;
            color: white;
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 1.5rem 0;
            }

            .action-buttons {
                position: relative;
                margin-bottom: 1rem;
            }

            .section-card .section-body {
                padding: 1rem;
            }
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="display-6 mb-2">
                <i class="fas fa-eye me-3"></i>@Model.Title
            </h1>
            <p class="lead mb-0 opacity-90">Hero section details and configuration overview</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                    </div>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary btn-lg">
                            <i class="fas fa-edit me-2"></i>Edit Hero Section
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status Badges -->
            <div class="status-badges">
                @if (Model.IsActive)
                {
                    <span class="badge bg-success badge-status">
                        <i class="fas fa-check-circle me-1"></i>Active
                    </span>
                }
                else
                {
                    <span class="badge bg-secondary badge-status">
                        <i class="fas fa-pause-circle me-1"></i>Inactive
                    </span>
                }
                <span class="badge bg-info badge-status">
                    <i class="fas fa-file-alt me-1"></i>@Model.PageName Page
                </span>
                @if (Model.EnableSlideshow)
                {
                    <span class="badge bg-primary badge-status">
                        <i class="fas fa-images me-1"></i>Slideshow Enabled
                    </span>
                }
                @if (Model.Slides.Any())
                {
                    <span class="badge bg-warning badge-status">
                        <i class="fas fa-layer-group me-1"></i>@Model.Slides.Count Slides
                    </span>
                }
            </div>

            <!-- Basic Information Section -->
            <div class="section-card">
                <div class="section-header">
                    <h5>
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                        <span class="badge ms-auto">Overview</span>
                    </h5>
                </div>
                <div class="section-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-heading me-1"></i>Title
                                </div>
                                <div class="detail-value">@Model.Title</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-file-alt me-1"></i>Page Assignment
                                </div>
                                <div class="detail-value">@Model.PageName</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-toggle-on me-1"></i>Status
                                </div>
                                <div class="detail-value">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-pause me-1"></i>Inactive
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-search me-1"></i>Meta Description
                                </div>
                                <div class="detail-value">@(Model.MetaDescription ?? "Not set")</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-tags me-1"></i>Meta Keywords
                                </div>
                                <div class="detail-value">@(Model.MetaKeywords ?? "Not set")</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-images me-1"></i>Slideshow
                                </div>
                                <div class="detail-value">
                                    @if (Model.EnableSlideshow)
                                    {
                                        <span class="badge bg-primary">
                                            <i class="fas fa-play me-1"></i>Enabled
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-stop me-1"></i>Disabled
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Content Section -->
            <div class="section-card">
                <div class="section-header">
                    <h5>
                        <i class="fas fa-edit me-2"></i>Hero Content
                        <span class="badge ms-auto">Primary</span>
                    </h5>
                </div>
                <div class="section-body">
                    <div class="detail-item">
                        <div class="detail-label">
                            <i class="fas fa-heading me-1"></i>Main Title
                        </div>
                        <div class="detail-value">
                            <div class="content-preview">
                                @Html.Raw(Model.MainTitle ?? "<em class='text-muted'>Not set</em>")
                            </div>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">
                            <i class="fas fa-text-height me-1"></i>Main Subtitle
                        </div>
                        <div class="detail-value">
                            <div class="content-preview">
                                @Html.Raw(Model.MainSubtitle ?? "<em class='text-muted'>Not set</em>")
                            </div>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">
                            <i class="fas fa-align-left me-1"></i>Main Description
                        </div>
                        <div class="detail-value">
                            <div class="content-preview">
                                @Html.Raw(Model.MainDescription ?? "<em class='text-muted'>Not set</em>")
                            </div>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">
                            <i class="fas fa-mouse-pointer me-1"></i>Call-to-Action Buttons
                        </div>
                        <div class="detail-value">
                            @if (!string.IsNullOrEmpty(Model.PrimaryButtonText))
                            {
                                <a href="@Model.PrimaryButtonUrl" class="button-preview button-primary">
                                    @Model.PrimaryButtonText
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(Model.SecondaryButtonText))
                            {
                                <a href="@Model.SecondaryButtonUrl" class="button-preview button-secondary">
                                    @Model.SecondaryButtonText
                                </a>
                            }
                            @if (string.IsNullOrEmpty(Model.PrimaryButtonText) && string.IsNullOrEmpty(Model.SecondaryButtonText))
                            {
                                <em class="text-muted">No buttons configured</em>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Slides Section -->
            @if (Model.Slides.Any())
            {
                <div class="section-card">
                    <div class="section-header">
                        <h5>
                            <i class="fas fa-images me-2"></i>Hero Slides
                            <span class="badge ms-auto">@Model.Slides.Count Slides</span>
                        </h5>
                    </div>
                    <div class="section-body">
                        @foreach (var slide in Model.Slides.OrderBy(s => s.DisplayOrder))
                        {
                            <div class="slide-card">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="slide-number">@slide.DisplayOrder</div>
                                            <div class="ms-3">
                                                <h6 class="mb-0">Slide @slide.DisplayOrder</h6>
                                                <div class="mt-1">
                                                    @if (slide.IsActive)
                                                    {
                                                        <span class="badge bg-success">Active</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    }
                                                    @if (slide.MediaType == "video")
                                                    {
                                                        <span class="badge bg-info">Video</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success">Image</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>

                                        @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                                        {
                                            <video class="media-preview" controls>
                                                <source src="@slide.VideoUrl" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        }
                                        else if (!string.IsNullOrEmpty(slide.ImageUrl))
                                        {
                                            <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="media-preview">
                                        }
                                        else
                                        {
                                            <div class="media-preview bg-light d-flex align-items-center justify-content-center">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-9">
                                        <div class="slide-content-preview">
                                            @Html.Raw(slide.Content)
                                        </div>

                                        @if (!string.IsNullOrEmpty(slide.ButtonText))
                                        {
                                            <div class="mt-3">
                                                <h6 class="text-primary mb-2">
                                                    <i class="fas fa-mouse-pointer me-1"></i>Slide Button
                                                </h6>
                                                <a href="@slide.ButtonUrl" class="button-preview button-primary">
                                                    @slide.ButtonText
                                                </a>
                                            </div>
                                        }

                                        @if (slide.MediaType == "video")
                                        {
                                            <div class="mt-3">
                                                <h6 class="text-info mb-2">
                                                    <i class="fas fa-video me-1"></i>Video Settings
                                                </h6>
                                                <div>
                                                    @if (slide.VideoAutoPlay) { <span class="badge bg-info me-1">Auto-play</span> }
                                                    @if (slide.VideoMuted) { <span class="badge bg-info me-1">Muted</span> }
                                                    @if (slide.VideoLoop) { <span class="badge bg-info me-1">Loop</span> }
                                                    @if (slide.VideoControls) { <span class="badge bg-info me-1">Controls</span> }
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Configuration Settings Section -->
            <div class="section-card">
                <div class="section-header">
                    <h5>
                        <i class="fas fa-cogs me-2"></i>Configuration Settings
                        <span class="badge ms-auto">Advanced</span>
                    </h5>
                </div>
                <div class="section-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-play-circle me-1"></i>Slideshow Controls
                            </h6>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-images me-1"></i>Slideshow Enabled
                                </div>
                                <div class="detail-value">
                                    @if (Model.EnableSlideshow)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Enabled
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Disabled
                                        </span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-play me-1"></i>Auto Play
                                </div>
                                <div class="detail-value">
                                    @if (Model.AutoPlay)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Yes
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>No
                                        </span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-circle me-1"></i>Navigation Dots
                                </div>
                                <div class="detail-value">
                                    @if (Model.ShowDots)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Visible
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Hidden
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-sliders-h me-1"></i>Advanced Options
                            </h6>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-chevron-left me-1"></i>Navigation Arrows
                                </div>
                                <div class="detail-value">
                                    @if (Model.ShowArrows)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Visible
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Hidden
                                        </span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-clock me-1"></i>Slideshow Speed
                                </div>
                                <div class="detail-value">
                                    <span class="badge bg-info">@Model.SlideshowSpeed ms</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">
                                    <i class="fas fa-magic me-1"></i>Floating Elements
                                </div>
                                <div class="detail-value">
                                    @if (Model.EnableFloatingElements)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Enabled
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Disabled
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="section-card">
                <div class="section-header">
                    <h5>
                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                    </h5>
                </div>
                <div class="section-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="detail-item">
                                <div class="h4 text-primary mb-1">@Model.Slides.Count</div>
                                <small class="text-muted">Total Slides</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="detail-item">
                                <div class="h4 text-success mb-1">@Model.Slides.Count(s => s.IsActive)</div>
                                <small class="text-muted">Active Slides</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <div class="section-header">
                    <h5>
                        <i class="fas fa-info-circle me-2"></i>Information
                    </h5>
                </div>
                <div class="section-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>Hero Section Tips</h6>
                        <ul class="mb-0 small">
                            <li>This hero section is displayed on the <strong>@Model.PageName</strong> page</li>
                            <li>@(Model.IsActive ? "Currently active and visible to visitors" : "Currently inactive and hidden from visitors")</li>
                            @if (Model.EnableSlideshow && Model.Slides.Any())
                            {
                                <li>Slideshow rotates through @Model.Slides.Count slides every @Model.SlideshowSpeed ms</li>
                            }
                            @if (!Model.Slides.Any())
                            {
                                <li>No slides configured - consider adding slides for dynamic content</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




