@model Technoloway.Web.Areas.Admin.Models.HeroSectionViewModel

@{
    ViewData["Title"] = "Hero Section Details";
    ViewData["PageTitle"] = Model.Title;
    ViewData["PageDescription"] = $"Details for {Model.Title} hero section";
}

@section Styles {
    <style>
        .details-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .details-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .details-body {
            padding: 1.5rem;
        }

        .detail-item {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: #6c757d;
        }

        .badge-status {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        .slide-preview {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .slide-preview:last-child {
            margin-bottom: 0;
        }

        .media-preview {
            max-width: 200px;
            max-height: 120px;
            border-radius: 0.25rem;
            object-fit: cover;
        }

        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            border-bottom-color: #667eea;
            color: #667eea;
            background: none;
            position: relative;
        }

        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px 2px 0 0;
        }

        .tab-content {
            padding-top: 1.5rem;
            min-height: 400px;
        }

        .tab-pane {
            animation: fadeIn 0.3s ease-in-out;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-content-preview {
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }

        .slide-content-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, white);
        }

        .slide-content-preview h1, .slide-content-preview h2, .slide-content-preview h3 {
            font-size: 1rem;
            margin: 0.25rem 0;
        }

        .slide-content-preview p {
            font-size: 0.875rem;
            margin: 0.25rem 0;
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-eye me-2 text-primary"></i>@Model.Title
            </h1>
            <p class="text-muted mb-0">Hero section details and configuration</p>
        </div>
        <div class="btn-group">
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>Edit
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Status Badge -->
    <div class="mb-3">
        @if (Model.IsActive)
        {
            <span class="badge bg-success badge-status">
                <i class="fas fa-check-circle me-1"></i>Active
            </span>
        }
        else
        {
            <span class="badge bg-secondary badge-status">
                <i class="fas fa-pause-circle me-1"></i>Inactive
            </span>
        }
        <span class="badge bg-info badge-status">
            <i class="fas fa-file-alt me-1"></i>@Model.PageName Page
        </span>
        @if (Model.EnableSlideshow)
        {
            <span class="badge bg-primary badge-status">
                <i class="fas fa-images me-1"></i>Slideshow Enabled
            </span>
        }
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                <i class="fas fa-info-circle me-1"></i>Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                <i class="fas fa-edit me-1"></i>Content
            </button>
        </li>
        @if (Model.Slides.Any())
        {
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="slides-tab" data-bs-toggle="tab" data-bs-target="#slides" type="button" role="tab" aria-controls="slides" aria-selected="false">
                    <i class="fas fa-images me-1"></i>Slides (@Model.Slides.Count)
                </button>
            </li>
        }
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">
                <i class="fas fa-cogs me-1"></i>Settings
            </button>
        </li>
    </ul>

    <div class="tab-content" id="detailsTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
            <div class="details-card">
                <div class="details-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="details-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Title</div>
                                <div class="detail-value">@Model.Title</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Page Assignment</div>
                                <div class="detail-value">@Model.PageName</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Status</div>
                                <div class="detail-value">
                                    @if (Model.IsActive)
                                    {
                                        <span class="text-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary">Inactive</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Meta Description</div>
                                <div class="detail-value">@(Model.MetaDescription ?? "Not set")</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Meta Keywords</div>
                                <div class="detail-value">@(Model.MetaKeywords ?? "Not set")</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Slideshow</div>
                                <div class="detail-value">
                                    @if (Model.EnableSlideshow)
                                    {
                                        <span class="text-success">Enabled</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary">Disabled</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Tab -->
        <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
            <div class="details-card">
                <div class="details-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Hero Content
                    </h5>
                </div>
                <div class="details-body">
                    <div class="detail-item">
                        <div class="detail-label">Main Title</div>
                        <div class="detail-value">@Html.Raw(Model.MainTitle ?? "Not set")</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Main Subtitle</div>
                        <div class="detail-value">@(Model.MainSubtitle ?? "Not set")</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Main Description</div>
                        <div class="detail-value">@(Model.MainDescription ?? "Not set")</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Primary Button</div>
                                <div class="detail-value">
                                    @if (!string.IsNullOrEmpty(Model.PrimaryButtonText))
                                    {
                                        <strong>@Model.PrimaryButtonText</strong><br>
                                        <small class="text-muted">@Model.PrimaryButtonUrl</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not configured</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Secondary Button</div>
                                <div class="detail-value">
                                    @if (!string.IsNullOrEmpty(Model.SecondaryButtonText))
                                    {
                                        <strong>@Model.SecondaryButtonText</strong><br>
                                        <small class="text-muted">@Model.SecondaryButtonUrl</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Not configured</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slides Tab -->
        @if (Model.Slides.Any())
        {
            <div class="tab-pane fade" id="slides" role="tabpanel" aria-labelledby="slides-tab">
                <div class="details-card">
                    <div class="details-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>Slides (@Model.Slides.Count)
                        </h5>
                    </div>
                    <div class="details-body">
                        @foreach (var slide in Model.Slides.OrderBy(s => s.DisplayOrder))
                        {
                            <div class="slide-preview">
                                <div class="row">
                                    <div class="col-md-3">
                                        @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                                        {
                                            <video class="media-preview" controls>
                                                <source src="@slide.VideoUrl" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                            <div class="mt-1">
                                                <small class="badge bg-info">Video</small>
                                            </div>
                                        }
                                        else if (!string.IsNullOrEmpty(slide.ImageUrl))
                                        {
                                            <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="media-preview">
                                            <div class="mt-1">
                                                <small class="badge bg-success">Image</small>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="media-preview bg-light d-flex align-items-center justify-content-center">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">Slide @slide.DisplayOrder</h6>
                                            <div>
                                                <span class="badge bg-primary">Order: @slide.DisplayOrder</span>
                                                @if (slide.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Inactive</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="slide-content-preview">
                                            @Html.Raw(slide.Content)
                                        </div>
                                        @if (!string.IsNullOrEmpty(slide.ButtonText))
                                        {
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong>Button:</strong> @slide.ButtonText
                                                    @if (!string.IsNullOrEmpty(slide.ButtonUrl))
                                                    {
                                                        <span> → @slide.ButtonUrl</span>
                                                    }
                                                </small>
                                            </div>
                                        }
                                        @if (slide.MediaType == "video")
                                        {
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong>Video Settings:</strong>
                                                    @if (slide.VideoAutoPlay) { <span class="badge bg-info">Auto-play</span> }
                                                    @if (slide.VideoMuted) { <span class="badge bg-info">Muted</span> }
                                                    @if (slide.VideoLoop) { <span class="badge bg-info">Loop</span> }
                                                    @if (slide.VideoControls) { <span class="badge bg-info">Controls</span> }
                                                </small>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Settings Tab -->
        <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
            <div class="details-card">
                <div class="details-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Configuration Settings
                    </h5>
                </div>
                <div class="details-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Slideshow Enabled</div>
                                <div class="detail-value">
                                    @if (Model.EnableSlideshow)
                                    {
                                        <span class="text-success"><i class="fas fa-check"></i> Yes</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary"><i class="fas fa-times"></i> No</span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Auto Play</div>
                                <div class="detail-value">
                                    @if (Model.AutoPlay)
                                    {
                                        <span class="text-success"><i class="fas fa-check"></i> Yes</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary"><i class="fas fa-times"></i> No</span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Show Navigation Dots</div>
                                <div class="detail-value">
                                    @if (Model.ShowDots)
                                    {
                                        <span class="text-success"><i class="fas fa-check"></i> Yes</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary"><i class="fas fa-times"></i> No</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <div class="detail-label">Show Navigation Arrows</div>
                                <div class="detail-value">
                                    @if (Model.ShowArrows)
                                    {
                                        <span class="text-success"><i class="fas fa-check"></i> Yes</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary"><i class="fas fa-times"></i> No</span>
                                    }
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Slideshow Speed</div>
                                <div class="detail-value">@Model.SlideshowSpeed ms</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Floating Elements</div>
                                <div class="detail-value">
                                    @if (Model.EnableFloatingElements)
                                    {
                                        <span class="text-success"><i class="fas fa-check"></i> Enabled</span>
                                    }
                                    else
                                    {
                                        <span class="text-secondary"><i class="fas fa-times"></i> Disabled</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    <script>
        // Initialize Bootstrap tabs - Enhanced debugging version
        function initializeTabs() {
            console.log('🚀 Starting Details tab initialization...');

            // Add visual indicator
            document.body.style.border = '3px solid blue';
            setTimeout(() => {
                document.body.style.border = 'none';
            }, 2000);

            // Check if Bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error('❌ Bootstrap is not loaded!');
                alert('Bootstrap is not loaded!');
                return;
            }
            console.log('✅ Bootstrap is available');

            // Initialize all tab triggers
            const tabTriggerList = document.querySelectorAll('#detailsTabs button[data-bs-toggle="tab"]');
            console.log('🔍 Found tab triggers:', tabTriggerList.length);

            // Log each tab trigger
            tabTriggerList.forEach((trigger, index) => {
                console.log(`Tab ${index + 1}:`, {
                    id: trigger.id,
                    target: trigger.getAttribute('data-bs-target'),
                    text: trigger.textContent.trim()
                });
            });

            if (tabTriggerList.length === 0) {
                console.error('❌ No tab triggers found!');
                alert('No tab triggers found!');
                return;
            }

            // Add click event listeners with multiple approaches
            tabTriggerList.forEach(function(tabTrigger, index) {
                console.log(`🔧 Adding click listener to tab ${index + 1}`);

                // Method 1: Direct click event
                tabTrigger.onclick = function(event) {
                    event.preventDefault();
                    console.log(`🖱️ ONCLICK - Details Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    switchTab(this, tabTriggerList);
                    return false;
                };

                // Method 2: addEventListener
                tabTrigger.addEventListener('click', function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.log(`🖱️ ADDEVENTLISTENER - Details Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    switchTab(this, tabTriggerList);
                    return false;
                }, true);

                // Method 3: mousedown event as backup
                tabTrigger.addEventListener('mousedown', function(event) {
                    console.log(`🖱️ MOUSEDOWN - Details Tab clicked (${index + 1}):`, this.getAttribute('data-bs-target'));
                    setTimeout(() => switchTab(this, tabTriggerList), 10);
                });
            });

            // Function to handle tab switching
            function switchTab(clickedTab, allTabs) {
                console.log('🔄 Switching tab to:', clickedTab.getAttribute('data-bs-target'));

                // Remove active class from all tabs
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                    tab.setAttribute('aria-selected', 'false');
                });

                // Add active class to clicked tab
                clickedTab.classList.add('active');
                clickedTab.setAttribute('aria-selected', 'true');

                // Hide all tab panes
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Show target tab pane
                const targetId = clickedTab.getAttribute('data-bs-target');
                console.log('🎯 Looking for tab pane with ID:', targetId);

                // Try multiple selector approaches
                let targetPane = document.querySelector(targetId);
                console.log('🔍 Method 1 - querySelector result:', targetPane);

                if (!targetPane) {
                    // Try without the # symbol
                    const idWithoutHash = targetId.substring(1);
                    targetPane = document.getElementById(idWithoutHash);
                    console.log('🔍 Method 2 - getElementById result:', targetPane);
                }

                if (!targetPane) {
                    // Try finding within the tab content container
                    const tabContent = document.getElementById('detailsTabContent');
                    if (tabContent) {
                        targetPane = tabContent.querySelector(targetId);
                        console.log('🔍 Method 3 - within tab content result:', targetPane);
                    }
                }

                if (!targetPane) {
                    // Try finding by class and id combination
                    targetPane = document.querySelector(`.tab-pane${targetId}`);
                    console.log('🔍 Method 4 - class+id selector result:', targetPane);
                }

                if (targetPane) {
                    console.log('📋 Target pane current classes:', targetPane.className);
                    targetPane.classList.add('show', 'active');
                    console.log('📋 Target pane new classes:', targetPane.className);
                    console.log('✅ Details Tab pane shown:', targetId);

                    // Force a visual check
                    targetPane.style.display = 'block';
                    targetPane.style.opacity = '1';
                    console.log('🎨 Applied inline styles for visibility');
                } else {
                    console.error('❌ Target pane not found:', targetId);
                    console.log('🔍 Available tab panes:');
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        console.log('  - ID:', pane.id, 'Classes:', pane.className);
                    });
                }
            }

            console.log('✅ Details Tabs initialized successfully');
            alert('Details Tabs initialized! Check console for details.');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs after a short delay to ensure DOM is ready
            setTimeout(() => {
                initializeTabs();
            }, 100);
        });
    </script>
}
