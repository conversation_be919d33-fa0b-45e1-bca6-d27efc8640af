@model Technoloway.Web.ViewComponents.ContactInfoViewModel

@{
    string GetContactIcon(string key)
    {
        return key.ToLower() switch
        {
            var k when k.Contains("address") => "fas fa-map-marker-alt",
            var k when k.Contains("phone") || k.Contains("mobile") => "fas fa-phone",
            var k when k.Contains("email") => "fas fa-envelope",
            var k when k.Contains("hours") || k.Contains("time") => "fas fa-clock",
            var k when k.Contains("website") => "fas fa-globe",
            var k when k.Contains("whatsapp") => "fab fa-whatsapp",
            var k when k.Contains("fax") => "fas fa-fax",
            _ => "fas fa-info-circle"
        };
    }

    string GetContactDisplayName(string key)
    {
        return key switch
        {
            "CompanyAddress" => "Our Location",
            "CompanyPhone" => "Phone Number",
            "CompanyEmail" => "Email Address",
            "WorkingHours" => "Working Hours",
            "ManagerPhone" => "Manager Phone",
            "SalesPhone" => "Sales Phone",
            "SupportPhone" => "Support Phone",
            "CompanyFax" => "Fax Number",
            "CompanyMobile" => "Mobile Number",
            "CompanyWhatsApp" => "WhatsApp",
            "CompanyWebsite" => "Website",
            _ => key.Replace("Company", "").Replace("Phone", " Phone").Replace("Email", " Email").Trim()
        };
    }
}

<div class="col-md-4">
    <h5>Contact Us</h5>
    <address>
        @if (Model.ContactSettings.Any())
        {
            @foreach (var setting in Model.ContactSettings.OrderBy(s => s.Key))
            {
                @if (!string.IsNullOrEmpty(setting.Value))
                {
                    <p>
                        @if (!string.IsNullOrEmpty(setting.Icon))
                        {
                            @if (setting.Icon.StartsWith("fa") || setting.Icon.Contains("fa-"))
                            {
                                <!-- FontAwesome icon -->
                                <i class="@setting.Icon me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                            }
                            else
                            {
                                <!-- Uploaded image icon -->
                                <img src="@setting.Icon" alt="@setting.Key icon" style="width: 20px; height: 20px; margin-right: 8px; display: inline-block;" />
                            }
                        }
                        else
                        {
                            <!-- Fallback FontAwesome icon -->
                            <i class="@GetContactIcon(setting.Key) me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                        }
                        @if (setting.Key.ToLower().Contains("email"))
                        {
                            <a href="mailto:@setting.Value" class="text-white text-decoration-none">@setting.Value</a>
                        }
                        else if (setting.Key.ToLower().Contains("phone") || setting.Key.ToLower().Contains("mobile"))
                        {
                            <a href="tel:@setting.Value" class="text-white text-decoration-none">@setting.Value</a>
                        }
                        else if (setting.Key.ToLower().Contains("website"))
                        {
                            <a href="@setting.Value" target="_blank" class="text-white text-decoration-none">@setting.Value</a>
                        }
                        else if (setting.Key.ToLower().Contains("whatsapp"))
                        {
                            <a href="https://wa.me/@setting.Value.Replace("+", "").Replace(" ", "").Replace("-", "")" target="_blank" class="text-white text-decoration-none">@setting.Value</a>
                        }
                        else
                        {
                            @setting.Value
                        }
                    </p>
                }
            }
        }



        @if (!Model.ContactSettings.Any())
        {
            <!-- Fallback contact info when no settings exist -->
            <p>
                <i class="fas fa-envelope me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                <a href="mailto:<EMAIL>" class="text-white text-decoration-none"><EMAIL></a>
            </p>
            <p>
                <i class="fas fa-phone me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                <a href="tel:+15551234567" class="text-white text-decoration-none">+****************</a>
            </p>
            <p>
                <i class="fas fa-map-marker-alt me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                123 Tech Street, Silicon Valley, CA 94043
            </p>
            <p>
                <i class="fas fa-clock me-2" style="color: #0d6efd; width: 20px; display: inline-block; background: none; border: none;"></i>
                Monday - Friday: 9:00 AM - 6:00 PM
            </p>
        }
    </address>
    <div class="mt-3">
        <a asp-controller="Home" asp-action="Contact" class="btn btn-outline-light btn-sm">
            <i class="fas fa-paper-plane me-2"></i>Send Message
        </a>
    </div>
</div>
