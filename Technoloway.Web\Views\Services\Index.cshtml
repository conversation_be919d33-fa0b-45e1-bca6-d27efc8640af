@model IEnumerable<Technoloway.Core.Entities.Service>

@{
    ViewData["Title"] = "Our Services";
    ViewData["MetaDescription"] = "Explore our comprehensive range of software development and IT services designed to help your business grow and succeed.";
    ViewData["MetaKeywords"] = "services, software development, web development, mobile apps, cloud solutions, IT consulting";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
@Html.Partial("_HeroSection")

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Services</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Our</span> Services
                        </h1>
                        <p class="page-subtitle">
                            Explore our comprehensive range of software development and IT services designed to help your business grow and succeed in the digital world.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="page-header-visual">
                        <div class="floating-element services-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="floating-element services-icon-2">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="floating-element services-icon-3">
                            <i class="fas fa-rocket"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Services Introduction - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Comprehensive Software</span> Solutions
                    </h2>
                    <p class="section-subtitle">
                        At Technoloway, we offer a wide range of software development and IT services designed to help your business grow and succeed in the digital world. Our team of experienced professionals is dedicated to delivering high-quality solutions tailored to your specific needs.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services List - Modern Design -->
<section class="modern-section services-grid-section">
    <div class="container">
        @if (Model != null && Model.Any())
        {
            <div class="row g-4">
                @{
                    int serviceIndex = 0;
                }
                @foreach (var service in Model)
                {
                    <div class="col-md-6 col-lg-3">
                        <div class="modern-card service-card-enhanced animate-on-scroll" data-delay="@((serviceIndex % 4 + 1) * 100)">
                            <div class="service-image-wrapper">
                                <div class="service-icon-display">
                                    @if (!string.IsNullOrEmpty(service.IconClass))
                                    {
                                        @if (service.IconClass.StartsWith("/images/"))
                                        {
                                            <img src="@service.IconClass" alt="@service.Name Icon" class="service-featured-icon" />
                                        }
                                        else
                                        {
                                            <i class="@service.IconClass service-featured-icon"></i>
                                        }
                                    }
                                    else
                                    {
                                        <i class="fas fa-cog service-featured-icon"></i>
                                    }
                                </div>
                                <div class="service-overlay">
                                    <div class="service-overlay-content">
                                        <p class="service-overlay-description">@service.Description</p>
                                        <a asp-action="Details" asp-route-id="@service.Id"
                                           class="modern-btn secondary">
                                            <span class="btn-text">Learn More</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-arrow-right"></i>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <h4 class="card-title">@service.Name</h4>
                            </div>
                        </div>
                    </div>
                    serviceIndex++;
                }
            </div>
        }
        else
        {
            <div class="modern-card empty-state-card animate-on-scroll">
                <div class="card-body text-center">
                    <div class="empty-state-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title">No Services Available</h3>
                    <p class="card-subtitle mb-4">We're building amazing services. Check back soon!</p>
                    <a href="/" class="modern-btn primary">
                        <span class="btn-text">Back to Home</span>
                        <span class="btn-icon">
                            <i class="fas fa-home"></i>
                        </span>
                    </a>
                </div>
            </div>
        }
    </div>
</section>

<!-- Why Choose Us - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Why Choose</span> Technoloway?
                    </h2>
                    <p class="section-subtitle">
                        We're committed to delivering exceptional software solutions that drive your business forward with innovation, quality, and dedicated support.
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="modern-card feature-card animate-on-scroll" data-delay="100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4 class="card-title">Expert Developers</h4>
                        <p class="card-subtitle">Our team consists of highly skilled developers with expertise in various technologies and domains.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card feature-card animate-on-scroll" data-delay="200">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4 class="card-title">Tailored Solutions</h4>
                        <p class="card-subtitle">We create custom solutions designed specifically to address your unique business challenges.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card feature-card animate-on-scroll" data-delay="300">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="card-title">Dedicated Support</h4>
                        <p class="card-subtitle">We provide ongoing support and maintenance to ensure your software runs smoothly.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="modern-card feature-card animate-on-scroll" data-delay="400">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="card-title">Results-Driven</h4>
                        <p class="card-subtitle">We focus on delivering solutions that help you achieve your business goals and drive growth.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="modern-card cta-card animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0 text-center text-lg-start">
                        <h2 class="section-title mb-3">
                            <span class="title-highlight">Ready to Transform</span> Your Business?
                        </h2>
                        <p class="section-subtitle mb-0">
                            Contact us today to discuss how our services can help you achieve your goals and drive your business forward with innovative solutions.
                        </p>
                    </div>
                    <div class="col-lg-4 text-center text-lg-end">
                        <div class="d-flex gap-3 justify-content-center justify-content-lg-end flex-wrap">
                            <a asp-controller="Home" asp-action="Contact" class="modern-btn primary">
                                <span class="btn-text">Get in Touch</span>
                                <span class="btn-icon">
                                    <i class="fas fa-phone"></i>
                                </span>
                            </a>
                            <a href="#services" class="modern-btn secondary">
                                <span class="btn-text">View Services</span>
                                <span class="btn-icon">
                                    <i class="fas fa-arrow-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Enhanced Service card hover effects with advanced micro-interactions
            const serviceCards = document.querySelectorAll('.service-card-enhanced');

            serviceCards.forEach(card => {
                // Add floating elements for enhanced interaction
                const floatingElements = document.createElement('div');
                floatingElements.className = 'floating-elements';
                for (let i = 0; i < 3; i++) {
                    const dot = document.createElement('div');
                    dot.className = 'floating-dot';
                    floatingElements.appendChild(dot);
                }
                card.appendChild(floatingElements);

                // Enhanced mouse enter effects
                card.addEventListener('mouseenter', function() {
                    // Add performance optimization classes
                    this.classList.add('will-change-transform');
                    const iconDisplay = this.querySelector('.service-icon-display');
                    const cardTitle = this.querySelector('.card-title');
                    const overlay = this.querySelector('.service-overlay');

                    if (iconDisplay) {
                        iconDisplay.classList.add('will-change-transform');
                    }
                    if (cardTitle) {
                        cardTitle.classList.add('will-change-transform');
                    }
                    if (overlay) {
                        overlay.style.opacity = '1';
                    }
                });

                // Enhanced mouse leave effects
                card.addEventListener('mouseleave', function() {
                    // Remove performance optimization classes
                    this.classList.remove('will-change-transform');
                    const iconDisplay = this.querySelector('.service-icon-display');
                    const cardTitle = this.querySelector('.card-title');
                    const overlay = this.querySelector('.service-overlay');

                    if (iconDisplay) {
                        iconDisplay.classList.remove('will-change-transform');
                    }
                    if (cardTitle) {
                        cardTitle.classList.remove('will-change-transform');
                    }
                    if (overlay) {
                        overlay.style.opacity = '0';
                    }
                });

                // Advanced ripple effect on click
                card.addEventListener('click', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const ripple = document.createElement('div');
                    ripple.className = 'ripple';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';

                    this.appendChild(ripple);

                    // Remove ripple after animation
                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }, 800);
                });

                // Touch device support
                if ('ontouchstart' in window) {
                    card.addEventListener('touchstart', function() {
                        this.classList.add('touch-active');
                    });

                    card.addEventListener('touchend', function() {
                        setTimeout(() => {
                            this.classList.remove('touch-active');
                        }, 150);
                    });
                }

                // Intersection Observer for performance optimization
                const cardObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('in-viewport');
                        } else {
                            entry.target.classList.remove('in-viewport');
                            // Remove performance classes when out of viewport
                            entry.target.classList.remove('will-change-transform');
                        }
                    });
                }, { threshold: 0.1 });

                cardObserver.observe(card);

                // Add subtle random animation delays for organic feel
                const randomDelay = Math.random() * 0.3;
                card.style.animationDelay = randomDelay + 's';
            });

            // Feature card hover effects
            const featureCards = document.querySelectorAll('.feature-card');

            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    const icon = this.querySelector('.feature-icon');
                    if (icon) {
                        icon.style.transform = 'scale(1.1) rotate(5deg)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const icon = this.querySelector('.feature-icon');
                    if (icon) {
                        icon.style.transform = 'scale(1) rotate(0deg)';
                    }
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}
